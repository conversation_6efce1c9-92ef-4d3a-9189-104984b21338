"use client";
import { useState, useRef, useEffect, ChangeEvent } from "react";
import dynamic from "next/dynamic";
import { useParams, useSearchParams } from "next/navigation";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import ReactMarkdown from "react-markdown";
import { Play, Loader2, X, AlertCircle, CheckCircle, HelpCircle } from "lucide-react";
import { useWebSocket } from "@/hooks/useWebSocket";
import { createSession } from "@/lib/api";
import PageTransition from "@/components/animations/PageTransition";
import { motion } from "framer-motion";
import type * as monaco from "monaco-editor";

const Monaco = dynamic(() => import("@monaco-editor/react"), { ssr: false });
const MotionButton = motion(Button);

interface InterviewPageProps {
  params: {
    role: "candidate" | "interviewer";
    sessionId: string;
  };
}

interface SessionDetails {
  candidate_name: string;
  interviewer_name: string;
  job_title: string;
  created_at: string;
  expires_at: string;
}

interface Artifact {
  id: number;
  session_id: string;
  artifact_type: 'question' | 'expected_output';
  content: string;
  created_at: string;
}

interface LatestArtifactsResponse {
  latest_question: Artifact | null;
  latest_output: Artifact | null;
}

interface AIAnalysisResult {
  final_verdict: {
    verdict: 'No Cheating Detected' | 'Potential Cheating Detected' | 'High-Confidence Cheating Detected';
    explanation: string;
    certainty_score: number;
    cheating_probability?: number;
  };
}

export default function InterviewPage() {
  const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";
  const params = useParams<{ role: string; sessionId: string }>();
  const searchParams = useSearchParams();
  const role = params.role as string;
  const sessionId = params.sessionId as string;
  const [language, setLanguage] = useState<"python" | "java">("python");
  const [code, setCode] = useState<string>("// Loading code...");
  const [output, setOutput] = useState<string>("");
  const [errorMsg, setErrorMsg] = useState<string>("");
  const [running, setRunning] = useState(false);
  const [question, setQuestion] = useState("");
  const [expectedOutput, setExpectedOutput] = useState("");
  const [presentedQuestion, setPresentedQuestion] = useState("");
  const [presentedOutput, setPresentedOutput] = useState("");
  const [sessionDetails, setSessionDetails] = useState<SessionDetails | null>(null);
  const [isLoadingDetails, setIsLoadingDetails] = useState(true);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [latestArtifactId, setLatestArtifactId] = useState<number | null>(null);
  const [aiAnalysisResult, setAiAnalysisResult] = useState<AIAnalysisResult | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisProgressMessage, setAnalysisProgressMessage] = useState<string | null>(null);
  const [analysisError, setAnalysisError] = useState<string | null>(null);
  const [activeAccordion, setActiveAccordion] = useState<string>("question");

  const isRemoteRef = useRef(false);
  const initialCodeFetchedRef = useRef(false); // To prevent re-fetching on language change if not desired
  const outputRef = useRef<HTMLDivElement>(null);
  const prevRunning = useRef(running);

  const { emitCodeChange, emitKeystroke, emitLanguageChange } = useWebSocket(
    sessionId,
    {
      onCodeUpdate: (payload) => {
        isRemoteRef.current = true;
        setCode(payload.fullCodeSnapshot); // Use the full snapshot for consistency
      },
      onExecutionResult: (payload) => {
        setOutput(payload.output || "");
        setErrorMsg(payload.error || "");
      },
      onLanguageUpdate: (payload) => {
        const newLang = payload.language as "python" | "java";
        setLanguage(newLang);
      },
      onArtifactPresented: (payload) => {
        if (payload.artifact_type === 'question') {
          setPresentedQuestion(payload.content);
          setLatestArtifactId(payload.id); // Fixed: use 'id' instead of 'artifact_id'
        } else if (payload.artifact_type === 'expected_output') {
          setPresentedOutput(payload.content);
        }
      },
      onAnalysisProgress: (payload) => {
        console.log("AI Analysis Progress:", payload);
        setAnalysisProgressMessage(payload.message);
      },
      onAnalysisComplete: (payload) => {
        console.log("AI Analysis Received:", payload);
        setIsAnalyzing(false);
        setAnalysisProgressMessage(null);
        if (payload.final_verdict) {
          setAiAnalysisResult(payload);
          setAnalysisError(null);
        } else {
          setAnalysisError("Analysis finished but no verdict was returned.");
          setAiAnalysisResult(null);
        }
      },
    }
  );

  useEffect(() => {
    if (!sessionId || initialCodeFetchedRef.current) {
      return;
    }

    const fetchInitialState = async () => {
      const token = searchParams.get("token"); // Get token for artifact fetching
      try {
        // Fetch initial language
        const langRes = await fetch(`${API_URL}/api/v1/sessions/${sessionId}/language`);
        let sessionLanguage: "python" | "java" = "python"; // Default
        if (langRes.ok) {
          const langData = await langRes.json();
          if (langData.language) {
            sessionLanguage = langData.language;
            setLanguage(langData.language);
          }
        } else {
          console.error("Failed to fetch session language:", langRes.status);
        }

        // Fetch latest code
        const codeRes = await fetch(`${API_URL}/api/v1/sessions/${sessionId}/latest_code`);
        if (codeRes.ok) {
          const codeData = await codeRes.json();
          if (codeData.latest_code_snapshot && codeData.latest_code_snapshot.trim() !== "") {
            setCode(codeData.latest_code_snapshot);
          } else {
            // If no snapshot, set default based on the fetched language
            setCode(
              sessionLanguage === "python"
                ? "# Welcome to your Python interview!\nprint('Hello, world!')\n"
                : `// Welcome to your Java interview!\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("Hello, world!");\n    }\n}`
            );
          }
        } else {
          console.error("Failed to fetch latest code:", codeRes.status);
          // Set default code if fetch fails
          setCode(
            sessionLanguage === "python"
              ? "# Error fetching code. Default Python code.\nprint('Hello, world!')\n"
              : `// Error fetching code. Default Java code.\npublic class Main {\n    public static void main(String[] args) {\n        System.out.println("Hello, world!");\n    }\n}`
          );
        }
        // Removed setCode(finalCode) as it's handled above
      } catch (error) {
        console.error("Error fetching initial state:", error);
        setCode("// Error loading code. Please try refreshing.");
        // Not setting initialCodeFetchedRef.current to true here, so it might retry or show error
      } finally {
        initialCodeFetchedRef.current = true;

        // Fetch latest artifacts
        if (token) { // Ensure token is available for authenticated request
          const artifactsRes = await fetch(`${API_URL}/api/v1/sessions/${sessionId}/latest_artifacts?token=${token}`);
          if (artifactsRes.ok) {
            const artifactsData: LatestArtifactsResponse = await artifactsRes.json();
            if (artifactsData.latest_question) {
              setPresentedQuestion(artifactsData.latest_question.content);
              setLatestArtifactId(artifactsData.latest_question.id);
            }
            if (artifactsData.latest_output) {
              setPresentedOutput(artifactsData.latest_output.content);
            }
          } else {
            console.error("Failed to fetch latest artifacts:", artifactsRes.status);
          }
        }
      }
    };

    fetchInitialState();
  }, [sessionId, searchParams]); // Added searchParams due to its use in fetchSessionDetails

  useEffect(() => {
    if (sessionId) {
      // Fetch session details
      const fetchSessionDetails = async () => {
        const token = searchParams.get("token");
        if (!token) {
          setErrorDetails("Session token not found in URL.");
          setIsLoadingDetails(false);
          return;
        }

        try {
          setIsLoadingDetails(true);
          const url = `${API_URL}/api/v1/sessions/${sessionId}/details?token=${token}`;
          const response = await fetch(url);
          if (!response.ok) {
            const errorData = await response.json().catch(() => ({ detail: "Failed to fetch session details" }));
            throw new Error(errorData.detail || "Failed to fetch session details");
          }
          const data: SessionDetails = await response.json();
          setSessionDetails(data);
          setErrorDetails(null); // Clear previous errors on success
        } catch (error) {
          console.error(error);
          setErrorDetails(
            error instanceof Error ? error.message : "An unknown error occurred"
          );
        } finally {
          setIsLoadingDetails(false);
        }
      };

      fetchSessionDetails();
    }
  }, [sessionId, searchParams]); // Added searchParams due to its use in fetchSessionDetails

  // Scroll to output when code execution finishes
  useEffect(() => {
    if (prevRunning.current && !running) { // Transitioned from running to not running
      outputRef.current?.scrollIntoView({ behavior: 'smooth' });
    }
    // Update the ref to the current value for the next render
    prevRunning.current = running;
  }, [running]);

  useEffect(() => {
    if (aiAnalysisResult && aiAnalysisResult.final_verdict.verdict !== 'No Cheating Detected') {
      setActiveAccordion("ai_analysis");
    }
  }, [aiAnalysisResult]);

  const handleEditorChange = (value?: string) => {
    const val = value || "";
    setCode(val);
    if (!isRemoteRef.current) {
      emitCodeChange({
        sessionId: sessionId,
        userType: role,
        changeType: "edit",
        content: null, // For granular changes, not fully used yet but good for structure
        positionStart: null, // For granular changes
        positionEnd: null, // For granular changes
        timestampMs: Date.now(),
        fullCodeSnapshot: val, // Key for persistence
        code: val, // Used by onCodeUpdate for client-side sync
      });
    }
    isRemoteRef.current = false;
  };

  const editorRef = useRef<any>(null);

  const handleKey = (e: monaco.IKeyboardEvent) => {
    emitKeystroke({
      sessionId: sessionId,
      userType: role,
      eventType: "keydown",
      keyCode: e.keyCode,
      keyChar: e.browserEvent.key,
      timestampMs: Date.now(),
      interKeyInterval: null,
      cursorPosition: null,
      selectionRange: null,
      modifiers: null,
    });
  };

  const handleEditorMount = (editor: any) => {
    editorRef.current = editor;
    editor.onKeyDown(handleKey);
  };

  const presentArtifact = async (artifactType: 'question' | 'expected_output') => {
    const content = artifactType === 'question' ? question : expectedOutput;
    if (!content) return;

    try {
      const res = await fetch(`${API_URL}/api/v1/sessions/${sessionId}/artifacts`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            artifact_type: artifactType,
            content: content,
          }),
        }
      );
      if (!res.ok) {
        console.error(`Failed to present ${artifactType}`);
      }
      // No need to set state here, will be updated via WebSocket
    } catch (error) {
      console.error(`Error presenting ${artifactType}:`, error);
    }
  };

  const runCode = async () => {
    setRunning(true);
    setOutput("");
    setErrorMsg("");
    setIsAnalyzing(true);
    setAnalysisProgressMessage("Starting analysis...");
    setAiAnalysisResult(null);
    setAnalysisError(null);

    try {
      const response = await fetch(`${API_URL}/api/v1/execute/${language}`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          session_id: sessionId,
          user_type: role,
          code: code,
          execution_start_ms: Date.now(),
          artifact_id: latestArtifactId
        }),
      });

      const result = await response.json();
      setOutput(result.output || "");
      setErrorMsg(result.error || "");
      
      // The backend now automatically triggers analysis. 
      // The frontend just needs to listen for the 'analysis_complete' event.

    } catch (error: any) {
      setErrorMsg(error.message);
      setIsAnalyzing(false); // Stop analyzing on error
    } finally {
      setRunning(false);
    }
  };

  return (
    <PageTransition>
      <div className="grid h-[calc(100vh-4rem)] p-4 grid-cols-1 md:grid-cols-3 gap-4">
        {/* Main Content Column */}
        <div className="md:col-span-2 flex flex-col gap-4">
          {/* Editor Card */}
          <div className="flex flex-col flex-grow">
            <div className="flex items-center justify-between pb-2">
              <h2 className="text-lg font-semibold">{role.toUpperCase()} Editor</h2>
              <div className="flex items-center gap-4">
                <select
                  value={language}
                  onChange={(e) => {
                    if (role !== "interviewer") return;
                    const newLang = e.target.value as "python" | "java";
                    emitLanguageChange({ sessionId, language: newLang });
                  }}
                  disabled={role !== "interviewer"}
                  className="border rounded px-2 py-1 text-sm bg-background disabled:opacity-70 disabled:cursor-not-allowed"
                >
                  <option value="python">Python</option>
                  <option value="java">Java</option>
                </select>
                <MotionButton
                  size="sm"
                  onClick={runCode}
                  disabled={running}
                  className="gap-1 min-w-[80px]"
                  whileTap={!running ? { scale: 0.97 } : {}}
                >
                  {running ? <Loader2 className="h-4 w-4 animate-spin" /> : <Play className="h-4 w-4" />}
                  {running ? "Running" : "Run"}
                </MotionButton>
              </div>
            </div>
            <div className="flex-1 overflow-hidden rounded border">
              <Monaco
                language={language}
                theme="vs-dark"
                value={code}
                onChange={handleEditorChange}
                options={{ minimap: { enabled: false } }}
                onMount={handleEditorMount}
              />
            </div>
          </div>
          {/* Output Card */}
          <div ref={outputRef} className="flex flex-col h-1/3">
            <h2 className="pb-2 text-lg font-semibold">Output</h2>
            <pre
              className={`flex-1 overflow-auto rounded border bg-muted p-4 text-sm whitespace-pre-wrap ${
                errorMsg ? "text-red-500" : ""
              }`}
            >
              {errorMsg || output}
            </pre>
          </div>
        </div>

        {/* Sidebar Column */}
        <div className="md:col-span-1 flex flex-col gap-4">
          {role === 'interviewer' ? (
            <>
              <Card>
                <CardHeader><CardTitle>Session Info</CardTitle></CardHeader>
                <CardContent>
                  {isLoadingDetails ? (
                    <p>Loading session details...</p>
                  ) : errorDetails ? (
                    <p className="text-red-500">{errorDetails}</p>
                  ) : sessionDetails ? (
                    <div className="space-y-2 text-sm">
                      <p>
                        <strong>Candidate:</strong> {sessionDetails.candidate_name}
                      </p>
                      <p>
                        <strong>Interviewer:</strong>{" "}
                        {sessionDetails.interviewer_name}
                      </p>
                      <p>
                        <strong>Role:</strong> {sessionDetails.job_title}
                      </p>
                      <p>
                        <strong>Created:</strong>{" "}
                        {new Date(sessionDetails.created_at).toLocaleString()}
                      </p>
                      <p>
                        <strong>Expires:</strong>{" "}
                        {new Date(sessionDetails.expires_at).toLocaleString()}
                      </p>
                    </div>
                  ) : (
                    <p>No session details available.</p>
                  )}
                </CardContent>
              </Card>
              <Card className="flex-grow flex flex-col">
                <CardHeader><CardTitle>Interview Tools</CardTitle></CardHeader>
                <CardContent className="flex-grow flex flex-col gap-4">
                  <Accordion type="single" collapsible className="w-full" value={activeAccordion} onValueChange={setActiveAccordion}>
                    <AccordionItem value="ai_analysis">
                      <AccordionTrigger>
                        <div className="flex items-center gap-2">
                          <span>AI Analysis</span>
                          {isAnalyzing && <Loader2 className="h-4 w-4 animate-spin" />}
                        </div>
                      </AccordionTrigger>
                      <AccordionContent>
                        <div className="pt-2 space-y-3">
                          {isAnalyzing && (
                            <div className="flex items-center gap-2 text-blue-500">
                              <Loader2 className="h-4 w-4 animate-spin" />
                              <span>{analysisProgressMessage || "Analyzing..."}</span>
                            </div>
                          )}
                          {analysisError && (
                            <div className="flex items-center gap-2 text-red-500">
                              <AlertCircle className="h-4 w-4" />
                              <span>Error: {analysisError}</span>
                            </div>
                          )}
                          {aiAnalysisResult && (
                            <div className="space-y-2">
                              <div className="flex items-center gap-2">
                                <span className="font-semibold">Verdict:</span>
                                <Badge
                                  variant={aiAnalysisResult.final_verdict.verdict.includes('High-Confidence') ? 'destructive'
                                    : aiAnalysisResult.final_verdict.verdict.includes('Potential') ? 'secondary'
                                    : 'default'}
                                >
                                  {aiAnalysisResult.final_verdict.verdict.includes('No Cheating') ? <CheckCircle className="h-3 w-3 mr-1" /> : <AlertCircle className="h-3 w-3 mr-1" />}
                                  {aiAnalysisResult.final_verdict.verdict}
                                </Badge>
                              </div>
                              <div className="text-sm space-y-1">
                                  <span className="font-semibold">Explanation:</span>
                                  <div className="prose prose-sm dark:prose-invert max-w-none rounded border bg-muted/30 p-2">
                                    <ReactMarkdown>
                                      {aiAnalysisResult.final_verdict.explanation}
                                    </ReactMarkdown>
                                  </div>
                                </div>
                              <p className="text-sm"><span className="font-semibold">Certainty:</span> {Math.round(aiAnalysisResult.final_verdict.certainty_score * 100)}%</p>
                              {aiAnalysisResult.final_verdict.cheating_probability && (
                                <p className="text-sm"><span className="font-semibold">Cheating Probability:</span> {Math.round(aiAnalysisResult.final_verdict.cheating_probability * 100)}%</p>
                              )}
                            </div>
                          )}
                          {!isAnalyzing && !aiAnalysisResult && !analysisError && (
                             <div className="flex items-center gap-2 text-muted-foreground">
                               <HelpCircle className="h-4 w-4" />
                               <span>Click "Run" to start AI analysis.</span>
                             </div>
                          )}
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                    <AccordionItem value="question">
                      <AccordionTrigger>Question to Present</AccordionTrigger>
                      <AccordionContent>
                        <div className="flex flex-col gap-2 pt-2">
                          <Textarea
                            value={question}
                            onChange={(e) => setQuestion(e.target.value)}
                            placeholder="Enter the coding question here..."
                            className="resize-none h-32"
                          />
                          <Button size="sm" onClick={() => presentArtifact('question')} className="mt-2">Present Question</Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                    <AccordionItem value="expected_output">
                      <AccordionTrigger>Expected Output</AccordionTrigger>
                      <AccordionContent>
                        <div className="flex flex-col gap-2 pt-2">
                          <Textarea
                            value={expectedOutput}
                            onChange={(e) => setExpectedOutput(e.target.value)}
                            placeholder="Enter the expected output here..."
                            className="resize-none h-32"
                          />
                          <Button size="sm" onClick={() => presentArtifact('expected_output')} className="mt-2">Present Output</Button>
                        </div>
                      </AccordionContent>
                    </AccordionItem>
                  </Accordion>
                </CardContent>
              </Card>
            </>
          ) : (
            <>
              <Card style={{ flexGrow: 1 }}> {/* Question Card - less growth */}
                <CardHeader><CardTitle>Question</CardTitle></CardHeader>
                <CardContent>
                  <pre className="text-sm whitespace-pre-wrap h-full overflow-y-auto min-h-[100px]">
                    {presentedQuestion || "Waiting for interviewer to present a question..."}
                  </pre>
                </CardContent>
              </Card>
              <Card style={{ flexGrow: 2 }}> {/* Output Card - more growth */}
                <CardHeader><CardTitle>Expected Output</CardTitle></CardHeader>
                <CardContent>
                  <pre className="text-sm whitespace-pre-wrap h-full overflow-y-auto min-h-[150px]">
                    {presentedOutput || "No expected output provided."}
                  </pre>
                </CardContent>
              </Card>
            </>
          )}
        </div>
      </div>
    </PageTransition>
  );
}
