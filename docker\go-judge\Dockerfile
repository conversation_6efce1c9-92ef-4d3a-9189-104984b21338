# Custom go-judge image with Python & Java support
# NOTE: This image is **only** used for local development.
# In production you should pin exact versions and use a slimmer base.

FROM criyle/go-judge:latest

# Install runtimes needed for interview questions
# We stick to Debian packages available in upstream image.
RUN apt-get update -qq \
    && DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
       python3 python3-minimal openjdk-17-jdk-headless \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Default port
EXPOSE 5050

# CMD left as upstream (defined in base image)
