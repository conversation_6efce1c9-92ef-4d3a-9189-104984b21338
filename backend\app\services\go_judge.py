"""Async wrapper for go-judge REST API (limited Python & Java support)."""
from __future__ import annotations

import asyncio
import os
from typing import Any, Dict, List

import httpx

GO_JUDGE_URL = os.getenv("GO_JUDGE_URL", "http://go-judge:5050")

# Resource limits (fairly generous for interview questions)
CPU_LIMIT_NS = 5_000_000_000  # 5s
MEM_LIMIT_BYTES = 256 * 1024 * 1024  # 256 MiB


class GoJudgeService:
    """Async context manager to communicate with go-judge."""

    def __init__(self) -> None:
        self.base_url = GO_JUDGE_URL.rstrip("/")
        self._client = httpx.AsyncClient(timeout=30.0)

    async def __aenter__(self) -> "GoJudgeService":
        return self

    async def __aexit__(self, *exc_info):  # type: ignore[override]
        await self._client.aclose()

    # ---------------------------------------------------------------------
    # Public helpers
    # ---------------------------------------------------------------------
    async def execute_python(self, code: str, stdin: str | bytes = "") -> Dict[str, Any]:
        payload = {
            "cmd": [
                {
                    "args": ["python3", "main.py"],
                    "env": ["PATH=/usr/local/bin:/usr/bin:/bin"],
                    "files": [
                        {"content": stdin},  # fd 0 → STDIN
                        {"name": "stdout", "max": 1_048_576, "pipe": True},
                        {"name": "stderr", "max": 1_048_576, "pipe": True},
                    ],
                    "cpuLimit": CPU_LIMIT_NS,
                    "memoryLimit": MEM_LIMIT_BYTES,
                    "procLimit": 10,
                    "copyIn": {"main.py": {"content": code}},
                    "copyOut": ["stdout", "stderr"],
                }
            ]
        }
        results = await self._post_run(payload)
        return self._parse_first(results)

    async def execute_java(self, code: str, stdin: str | bytes = "") -> Dict[str, Any]:
        """Compile then run Java; detects the public class name automatically."""
        import re
        m = re.search(r"public\s+class\s+(\w+)", code)
        class_name = m.group(1) if m else "Main"
        java_file = f"{class_name}.java"

        # 1) Compile
        compile_req = {
            "cmd": [
                {
                    "args": ["javac", java_file],
                    "env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"],
                    "files": [
                        {"content": ""},
                        {"name": "stdout", "max": 1_048_576, "pipe": True},
                        {"name": "stderr", "max": 1_048_576, "pipe": True},
                    ],
                    "cpuLimit": 10_000_000_000,  # 10s
                    "memoryLimit": MEM_LIMIT_BYTES,
                    "procLimit": 50,
                    "copyIn": {java_file: {"content": code}},
                    "copyOut": ["stdout", "stderr", f"{class_name}.class"],
                    "copyOutCached": [f"{class_name}.class"],
                }
            ]
        }
        compile_results = await self._post_run(compile_req)
        compile_out = self._parse_first(compile_results)

        # Bail early if compilation failed
        if compile_out.get("exit_status", 0) != 0:
            return compile_out

        file_id = compile_results[0].get("fileIds", {}).get(f"{class_name}.class")
        if not file_id:
            return {
                "stdout": compile_out.get("stdout"),
                "stderr": f"{class_name}.class not found after compilation.",
                "time": compile_out.get("time"),
                "memory": compile_out.get("memory"),
            }

        # 2) Run
        run_req = {
            "cmd": [
                {
                    
                    "args": ["java", class_name],
                    "env": ["PATH=/usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin"],
                    "files": [
                        {"content": stdin},
                        {"name": "stdout", "max": 1_048_576, "pipe": True},
                        {"name": "stderr", "max": 1_048_576, "pipe": True},
                    ],
                    "cpuLimit": CPU_LIMIT_NS,
                    "memoryLimit": MEM_LIMIT_BYTES,
                    "procLimit": 50,
                    "copyIn": {f"{class_name}.class": {"fileId": file_id}},
                    "copyOut": ["stdout", "stderr"],
                }
            ]
        }
        run_results = await self._post_run(run_req)
        return self._parse_first(run_results)

    # ------------------------------------------------------------------
    # Internal helpers
    # ------------------------------------------------------------------
    async def _post_run(self, payload: Dict[str, Any]) -> List[Dict[str, Any]]:  # noqa: D401
        resp = await self._client.post(f"{self.base_url}/run", json=payload)
        resp.raise_for_status()
        return resp.json()

    @staticmethod
    def _parse_first(results: List[Dict[str, Any]]) -> Dict[str, Any]:
        if not results:
            return {"stdout": None, "stderr": "No result", "time": None, "memory": None}
        res = results[0]
        files = res.get("files", {})
        return {
            "stdout": files.get("stdout"),
            "stderr": files.get("stderr"),
            "time": res.get("time"),
            "memory": res.get("memory"),
            "exit_status": res.get("exitStatus"),
            "status": res.get("status"),
            "file_error": res.get("fileError"),
        }
