{"extends": "./tsconfig.base.json", "compilerOptions": {"baseUrl": ".", "paths": {"@/*": ["src/*"]}, "strict": true, "skipLibCheck": true, "types": ["node"], "jsx": "preserve", "lib": ["dom", "dom.iterable", "esnext"], "moduleResolution": "node", "target": "ES2017", "allowJs": true, "noEmit": true, "incremental": true, "module": "esnext", "resolveJsonModule": true, "isolatedModules": true, "plugins": [{"name": "next"}]}, "include": ["next-env.d.ts", ".next/types/**/*.ts", "**/*.ts", "**/*.tsx"], "exclude": ["node_modules"]}