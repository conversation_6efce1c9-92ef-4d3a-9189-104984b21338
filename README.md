# Code Guardians – Real-time Collaborative Coding Interview Platform (POC)

This repository contains a proof-of-concept platform for conducting technical interviews while capturing rich telemetry for future AI-based cheating detection.

## Stack

| Layer          | Tech                                   |
| -------------- | -------------------------------------- |
| Frontend       | Next.js 15 / React 19 / TypeScript 5.6 |
| Styling        | Tailwind CSS 3 & Shadcn/ui             |
| Editor         | Monaco Editor 0.52                     |
| Real-time      | Socket.io 4.8                          |
| Backend        | FastAPI (Python 3.12) + Socket.io      |
| AI Orchestration | LangChain & LangGraph                  |
| Experiment Tracking | MLflow 3.1                             |
| Database       | PostgreSQL 16 (via SQLAlchemy)         |
| Sessions       | Redis 7                                |
| Code Runner    | go-judge (custom image: Python 3.12 + OpenJDK 17) |
| Dev Environment| Docker Compose v2                      |

## Quick start (local)

```bash
# clone repo then from project root
cp .env.example .env  # adjust if needed (frontend/back-end examples also available)

# build & start all services
docker compose up --build -d

# view logs
docker compose logs -f backend
```

Frontend: http://localhost:3000  
Backend API & WS: http://localhost:8000
MLflow UI: http://localhost:5001

> ℹ️  The first run can take several minutes while Docker images build.

## Project Structure

```
.
├── backend/        # FastAPI service
├── frontend/       # Next.js app
├── init-db.sql     # Creates core DB schema
├── docker/go-judge/  # Custom go-judge Dockerfile with runtimes
└── docker-compose.yml
```

## Core Features

### Real-time Collaboration
- **Synchronized Code Editor**: Candidates and interviewers share a real-time Monaco editor session.
- **Live Language Switching**: Interviewers can switch the coding language (`Python`/`Java`) for the session, which is reflected instantly for the candidate.
- **Real-time Execution**: Code execution results are broadcast to both participants immediately.

### AI-Powered Cheating Detection
- **Asynchronous Analysis**: When code is run, an AI analysis is triggered in the background without blocking the user.
- **Multi-Agent System**: A sophisticated multi-agent graph built with **LangGraph** analyzes the candidate's solution for potential cheating.
  - A smaller, faster model generates potential solutions to the given problem.
  - A larger model compares the candidate's code against the generated solutions to form a verdict.
- **Dynamic UI**: The analysis results, including a verdict, explanation, and certainty score, are displayed in the interviewer's UI. The explanation is rendered as formatted markdown for readability.

### MLflow Experiment Tracking
- **Deep Tracing**: Every AI analysis run is logged as an experiment in **MLflow**.
- **LangChain Autologging**: Integration with `mlflow.langchain.autolog()` automatically captures detailed traces of the LangGraph execution, providing full visibility into the agent's decision-making process.
- **Persistent Artifacts**: The MLflow server, running in Docker, is configured with persistent storage for both metadata and artifacts, ensuring that trace data is always accessible via the UI.

## Apple Silicon (M-series) caveat

go-judge relies on nested containers. Docker Desktop with the default virtualization framework usually works fine on M-series Macs. If you see sandbox-related errors, run Docker under Colima or use an Ubuntu VM to provide proper cgroup support.

Options:
1. **Use Colima / Lima** – runs Docker inside a lightweight Linux VM that supports cgroups.
   ```bash
   brew install colima
   colima start --arch x86_64 --memory 4 --cpu 4
   docker compose up -d    # inside the same terminal
   ```
2. **Switch to an Intel / Windows / native-Linux machine** – the existing compose file works out of the box.
3. **Temporarily point to a hosted runner** – set `GO_JUDGE_URL=https://judge.mycompany.internal` (no local sandbox).

---

## Database Schema

The database schema is defined in `backend/app/models/schemas.py` and initialized by `init-db.sql`. It consists of the following tables:

### `interview_sessions`

Stores metadata for each interview session.

| Column             | Type        | Description                                                                 |
| ------------------ | ----------- | --------------------------------------------------------------------------- |
| `id`               | `UUID`      | Primary key for the session.                                                |
| `admin_id`         | `INTEGER`   | Foreign key to the admin who created the session (currently placeholder).   |
| `candidate_name`   | `VARCHAR`   | Name of the candidate.                                                      |
| `interviewer_name` | `VARCHAR`   | Name of the interviewer.                                                    |
| `job_title`        | `VARCHAR`   | Job title for the position.                                                 |
| `candidate_token`  | `VARCHAR`   | Unique token for the candidate's interview link.                            |
| `interviewer_token`| `VARCHAR`   | Unique token for the interviewer's interview link.                          |
| `expires_at`       | `TIMESTAMP` | When the session links expire.                                              |
| `created_at`       | `TIMESTAMP` | When the session was created.                                               |
| `is_active`        | `BOOLEAN`   | Whether the session is currently active.                                    |
| `language`         | `VARCHAR`   | The programming language for the session (e.g., `python`, `java`).          |

### `keystroke_events`

Captures every keystroke during the interview for detailed telemetry.

| Column               | Type        | Description                                                                 |
| -------------------- | ----------- | --------------------------------------------------------------------------- |
| `id`                 | `BIGINT`    | Primary key for the event.                                                  |
| `session_id`         | `UUID`      | Foreign key to `interview_sessions`.                                        |
| `user_type`          | `VARCHAR`   | `candidate` or `interviewer`.                                               |
| `event_type`         | `VARCHAR`   | The type of key event (e.g., `keydown`).                                    |
| `key_code`           | `INTEGER`   | The key code of the pressed key.                                            |
| `key_char`           | `VARCHAR`   | The character of the pressed key.                                           |
| `timestamp_ms`       | `BIGINT`    | The client-side timestamp in milliseconds.                                  |
| `inter_key_interval` | `INTEGER`   | Time in ms since the previous keystroke (currently placeholder).            |
| `cursor_position`    | `JSON`      | The cursor position in the editor (currently placeholder).                  |
| `selection_range`    | `JSON`      | The selected text range in the editor (currently placeholder).              |
| `modifiers`          | `JSON`      | Any modifier keys pressed (e.g., `Shift`, `Ctrl`) (currently placeholder).  |

### `code_changes`

Stores a snapshot of the code whenever a change is made.

| Column               | Type     | Description                                                                 |
| -------------------- | -------- | --------------------------------------------------------------------------- |
| `id`                 | `BIGINT` | Primary key for the event.                                                  |
| `session_id`         | `UUID`   | Foreign key to `interview_sessions`.                                        |
| `user_type`          | `VARCHAR`| `candidate` or `interviewer`.                                               |
| `change_type`        | `VARCHAR`| The type of change (e.g., `edit`).                                          |
| `full_code_snapshot` | `TEXT`   | A complete snapshot of the code after the change.                           |
| `timestamp_ms`       | `BIGINT` | The client-side timestamp in milliseconds.                                  |

### `execution_events`

Logs every code execution attempt and its result.

| Column               | Type      | Description                                                                 |
| -------------------- | --------- | --------------------------------------------------------------------------- |
| `id`                 | `BIGINT`  | Primary key for the event.                                                  |
| `session_id`         | `UUID`    | Foreign key to `interview_sessions`.                                        |
| `user_type`          | `VARCHAR` | `candidate` or `interviewer`.                                               |
| `artifact_id`        | `BIGINT`  | Foreign key to `interview_artifacts`, linking execution to a question. Can be `NULL`. |
| `code_snapshot`      | `TEXT`    | The code that was executed.                                                 |
| `execution_start_ms` | `BIGINT`  | The client-side timestamp when execution was triggered.                     |
| `execution_end_ms`   | `BIGINT`  | The server-side timestamp when execution finished.                          |
| `output`             | `TEXT`    | The `stdout` from the execution.                                            |
| `error_message`      | `TEXT`    | The `stderr` from the execution.                                            |
| `memory_usage`       | `INTEGER` | Memory used by the execution in bytes.                                      |
| `cpu_time_ms`        | `INTEGER` | CPU time used by the execution in milliseconds.                             |

### `interview_artifacts`

Stores questions and expected outputs presented by the interviewer.

| Column          | Type      | Description                                          |
| --------------- | --------- | ---------------------------------------------------- |
| `id`            | `BIGINT`  | Primary key for the artifact.                        |
| `session_id`    | `UUID`    | Foreign key to `interview_sessions`.                 |
| `artifact_type` | `VARCHAR` | `question` or `expected_output`.                     |
| `content`       | `TEXT`    | The text content of the artifact.                    |
| `created_at`    | `TIMESTAMP`| When the artifact was created.                       |

---

## API Endpoints

The backend exposes RESTful endpoints for managing sessions and executing code.

### Session Management (`/sessions`)

- `GET /`: Lists all interview sessions.
- `POST /`: Creates a new interview session.
- `DELETE /{session_id}`: Deletes a session.
- `GET /{session_id}/latest_code`: Retrieves the last saved code snapshot for a session.
- `GET /{session_id}/language`: Retrieves the current language for a session.
- `GET /{session_id}/details?token={token}`: Retrieves session details (candidate/interviewer names, etc.). Requires a valid session participant token.
- `POST /{session_id}/artifacts`: Submits a new artifact (question or expected output).
- `GET /{session_id}/latest_artifacts?token={token}`: Retrieves the most recently presented question and expected output. Requires a valid session participant token.

### Code Execution (`/execute`)

- `POST /python`: Executes a Python code snippet.
- `POST /java`: Executes a Java code snippet.

### AI Cheating Detection (`/api/v1/sessions`)

The platform includes a multi-agent AI system to analyze candidate code for potential cheating. This is triggered automatically on the backend when an interviewer runs a candidate's code.

- `POST /{session_id}/analyze`: Analyzes the candidate's code against the provided question.

#### Environment Variables for AI Analysis

To enable this feature, you must configure the following environment variables in the `backend/.env` file:

| Variable              | Description                                                                                                       |
| --------------------- | ----------------------------------------------------------------------------------------------------------------- |
| `LLM_PROVIDER`        | The provider to use. Defaults to `google`.                                                                        |
| `GOOGLE_API_KEY`      | Required if `LLM_PROVIDER` is `google`.                                                                          |
| `OPENROUTER_API_KEY`  | Required if `LLM_PROVIDER` is `openrouter`.                                                                      |
| `OPENROUTER_API_BASE` | Optional. The base URL for the OpenRouter API. Defaults to `https://openrouter.ai/api/v1`.                        |
| `SOLUTION_MODEL`      | The model for generating solutions.                                                                              |
| `ANALYSIS_MODEL`      | The model for the initial, fast analysis.                                                                        |
| `EXPERT_MODEL`        | The model for the expert, in-depth analysis.                                                                     |
| `MLFLOW_TRACKING_URI` | **Required to enable tracing**. The URI of your MLflow tracking server (e.g., `http://127.0.0.1:5001`).          |
| `LANGCHAIN_PROJECT`   | An optional project name for grouping runs in LangSmith. Defaults to `code-guardians-analysis`.                   |

*Model Naming Convention:*
- For **Google**: Use the model name directly (e.g., `google/gemini-2.5-pro`).
- For **OpenRouter**: Use the `provider/model` format (e.g., `google/google/gemini-2.5-pro`).

---

## WebSocket Events

Real-time communication is handled via Socket.IO.

### Client-to-Server Events

- `join_session`: Client requests to join a specific session room.
- `code_change`: Sent when a user modifies the code in the editor.
- `keystroke`: Sent on every key press in the editor.
- `language_change`: Sent when the interviewer changes the programming language.
- `execution_result`: Sent by a client to broadcast the result of a code execution.

### Server-to-Client Events

- `code_update`: Broadcasts the latest code snapshot to all clients in a session.
- `keystroke_update`: Broadcasts keystroke data (for real-time typing indicators, etc.).
- `language_update`: Broadcasts the new language to all clients in a session.
- `execution_result`: Broadcasts the result of a code execution to all other participants in the session.
- `artifact_presented`: Broadcasts a new question or expected output to all clients in a session.

---

## Development workflow
1. Modify code in `frontend/` or `backend/` – containers use hot-reload.
2. Run `docker compose exec backend alembic upgrade head` after model changes (migrations TBD).
3. PRs & commits must keep eslint/ruff clean (CI coming).

## License
Publicis Sapient internal POC – not for external distribution.
