"""Database engine and async session factory."""
import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker
from sqlalchemy import event
from sqlalchemy.engine import Engine

from app.core.logging import get_logger

# Initialize database logger
db_logger = get_logger("database")

DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql+asyncpg://user:password@postgres:5432/interview_platform",
)

# Echo can be toggled with env var for debug
echo_sql = os.getenv("DB_ECHO", "false").lower() == "true"
engine = create_async_engine(DATABASE_URL, echo=echo_sql, future=True)

# Add database event listeners for logging
@event.listens_for(Engine, "before_cursor_execute")
def before_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log SQL queries before execution."""
    db_logger.debug("Executing SQL query",
                   statement=statement[:200] + "..." if len(statement) > 200 else statement,
                   parameters=str(parameters)[:100] + "..." if len(str(parameters)) > 100 else str(parameters))

@event.listens_for(Engine, "after_cursor_execute")
def after_cursor_execute(conn, cursor, statement, parameters, context, executemany):
    """Log SQL query completion."""
    db_logger.debug("SQL query completed")

async_session: async_sessionmaker[AsyncSession] = async_sessionmaker(
    engine, expire_on_commit=False, class_=AsyncSession
)
