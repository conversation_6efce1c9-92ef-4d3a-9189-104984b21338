"""Database engine and async session factory."""
import os
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine, async_sessionmaker


DATABASE_URL = os.getenv(
    "DATABASE_URL",
    "postgresql+asyncpg://user:password@postgres:5432/interview_platform",
)

# Echo can be toggled with env var for debug
engine = create_async_engine(DATABASE_URL, echo=False, future=True)

async_session: async_sessionmaker[AsyncSession] = async_sessionmaker(
    engine, expire_on_commit=False, class_=AsyncSession
)
