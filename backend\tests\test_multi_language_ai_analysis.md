# Multi-Language AI Analysis Testing Plan

## Overview
This document outlines comprehensive test scenarios to verify that the AI analysis system correctly handles both Python and Java programming languages, including language switching during sessions and prevention of cross-language contamination.

## Test Categories

### 1. Basic Language-Specific Analysis Tests

#### Test 1.1: Python Code Analysis
**Objective**: Verify AI analysis works correctly for Python code
**Setup**:
- Create session with `language: "python"`
- Add a Python coding question (e.g., "Implement a function to find the maximum element in a list")
- Submit Python candidate code

**Expected Behavior**:
- Solution generation node generates Python solutions
- Initial analysis compares Python code against Python solutions
- System prompts reference "PYTHON" language context
- Logging includes `programming_language: "python"`

#### Test 1.2: Java Code Analysis  
**Objective**: Verify AI analysis works correctly for Java code
**Setup**:
- Create session with `language: "java"`
- Add a Java coding question (e.g., "Implement a method to reverse a string")
- Submit Java candidate code

**Expected Behavior**:
- Solution generation node generates Java solutions with proper class structure
- Initial analysis compares Java code against Java solutions
- System prompts reference "JAVA" language context
- Logging includes `programming_language: "java"`

### 2. Language Switching Tests

#### Test 2.1: Mid-Session Language Switch
**Objective**: Verify language switching updates AI analysis behavior
**Setup**:
1. Create session with `language: "python"`
2. Submit Python code and trigger analysis
3. Switch language to Java via WebSocket `language_change` event
4. Submit Java code and trigger analysis

**Expected Behavior**:
- First analysis uses Python context and generates Python solutions
- After language switch, second analysis uses Java context and generates Java solutions
- Database correctly updates session language
- All participants receive `language_update` WebSocket event

#### Test 2.2: Multiple Language Switches
**Objective**: Verify system handles multiple language changes correctly
**Setup**:
1. Start with Python → analyze code
2. Switch to Java → analyze code  
3. Switch back to Python → analyze code

**Expected Behavior**:
- Each analysis uses the correct language context
- No cross-contamination between language contexts
- Consistent behavior across switches

### 3. Default Language Handling Tests

#### Test 3.1: Session Without Language Set
**Objective**: Verify default language behavior
**Setup**:
- Create session with `language: null`
- Submit code and trigger analysis

**Expected Behavior**:
- System defaults to Python (`programming_language = "python"`)
- Warning logged: "No programming language found for session, defaulting to Python"
- Analysis proceeds with Python context

#### Test 3.2: Invalid Language Value
**Objective**: Verify handling of invalid language values
**Setup**:
- Manually set session language to invalid value (e.g., "javascript")
- Submit code and trigger analysis

**Expected Behavior**:
- System should handle gracefully (may default to Python or pass through)
- Analysis should not crash
- Appropriate logging of the situation

### 4. Cross-Language Contamination Prevention Tests

#### Test 4.1: Python Session with Java-like Code
**Objective**: Verify Python analysis doesn't get confused by Java-style syntax
**Setup**:
- Python session
- Submit Python code that uses Java-like patterns (e.g., verbose variable names, explicit typing)

**Expected Behavior**:
- Generated solutions are pure Python
- Analysis considers Python conventions, not Java conventions
- No false positives due to Java-style patterns

#### Test 4.2: Java Session with Python-like Code  
**Objective**: Verify Java analysis doesn't get confused by Python-style syntax
**Setup**:
- Java session
- Submit Java code that uses Python-like patterns (e.g., snake_case variables)

**Expected Behavior**:
- Generated solutions are proper Java with camelCase conventions
- Analysis considers Java conventions
- Appropriate detection of non-standard Java patterns

### 5. Integration Tests

#### Test 5.1: End-to-End Language Flow
**Objective**: Test complete flow from frontend to AI analysis
**Setup**:
1. Frontend creates session with specific language
2. Frontend switches language via UI
3. Frontend executes code triggering analysis

**Expected Behavior**:
- Language persists correctly through all layers
- WebSocket events propagate language changes
- AI analysis uses correct language context

#### Test 5.2: Manual Analysis API
**Objective**: Verify manual analysis endpoint respects session language
**Setup**:
- Create session with specific language
- Use `/api/v1/analysis/{session_id}` endpoint to trigger manual analysis

**Expected Behavior**:
- Manual analysis fetches and uses session language
- Same behavior as automatic analysis triggers

### 6. Error Handling Tests

#### Test 6.1: Database Connection Issues During Language Fetch
**Objective**: Verify graceful handling of database errors
**Setup**:
- Simulate database connection failure during language fetch
- Attempt to trigger analysis

**Expected Behavior**:
- Appropriate error logging
- Analysis either fails gracefully or defaults to Python
- No system crashes

#### Test 6.2: Session Not Found
**Objective**: Verify handling of invalid session IDs
**Setup**:
- Attempt analysis with non-existent session ID

**Expected Behavior**:
- Appropriate error handling
- Clear error messages in logs
- No system crashes

## Test Implementation Notes

### Required Test Data
- Sample Python questions and solutions
- Sample Java questions and solutions  
- Valid and invalid session configurations
- Test code snippets in both languages

### Verification Points
- Database queries for session language
- System prompt content includes correct language references
- Generated solutions use correct language syntax
- Logging includes programming language context
- WebSocket events include language information

### Performance Considerations
- Language switching should not significantly impact analysis performance
- Multiple language switches should not cause memory leaks
- Database queries for language should be efficient

## Success Criteria
- All language-specific analyses produce appropriate results
- Language switching works seamlessly without contamination
- Default language handling is robust
- Error conditions are handled gracefully
- Performance remains acceptable across language switches
