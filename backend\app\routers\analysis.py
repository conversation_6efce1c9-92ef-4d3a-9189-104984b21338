from fastapi import <PERSON><PERSON>outer, BackgroundTasks, Depends, HTTPException
from fastapi.responses import JSONResponse
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession
from app.core.logging import get_logger
from app.database import get_db
from app.models.schemas import InterviewSession
from app.services.ai_analysis_service import (
    stream_ai_analysis,
    AnalysisPayload
)
from pydantic import BaseModel

# Initialize logging
logger = get_logger("analysis")

class AnalysisRequest(BaseModel):
    question: str
    candidate_code: str

def create_analysis_router(sio_instance):
    router = APIRouter()

    @router.post("/sessions/{session_id}/analyze", status_code=202, tags=["Analysis"])
    async def analyze_code_endpoint(
        session_id: str,
        request: AnalysisRequest,
        background_tasks: BackgroundTasks,
        db: AsyncSession = Depends(get_db)
    ):
        """
        Triggers the AI analysis of a candidate's code for a given session.
        This endpoint is useful for manual testing and re-triggering analysis.
        """
        logger.info("Manual AI analysis requested",
                   session_id=session_id,
                   code_length=len(request.candidate_code),
                   question_length=len(request.question))

        try:
            # Fetch the session language
            session_result = await db.execute(
                select(InterviewSession.language).where(
                    InterviewSession.id == session_id
                )
            )
            programming_language = session_result.scalar_one_or_none()

            # Default to Python if no language is set
            if not programming_language:
                programming_language = "python"
                logger.warning("No programming language found for session, defaulting to Python",
                             session_id=session_id)

            payload = AnalysisPayload(
                question=request.question,
                candidate_code=request.candidate_code,
                session_id=session_id,
                programming_language=programming_language
            )

            # Run the full analysis in the background, including WebSocket broadcast
            background_tasks.add_task(stream_ai_analysis, db, payload, sio_instance)
            logger.info("AI analysis task scheduled",
                       session_id=session_id,
                       programming_language=programming_language)
            return {"message": "Analysis started in the background."}

        except Exception as e:
            logger.error("Failed to start AI analysis",
                        session_id=session_id,
                        error=str(e))
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred: {str(e)}")

    return router
