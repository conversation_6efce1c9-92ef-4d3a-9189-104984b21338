"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

// Home route: decide where to send user based on presence of admin token.
export default function Home() {
  const router = useRouter();

  useEffect(() => {
    const token = typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;
    router.replace(token ? "/admin/sessions" : "/admin/login");
  }, [router]);

  return null; // optional: could render a spinner
}

