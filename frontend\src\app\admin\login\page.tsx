"use client";
import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { login, storeToken } from "@/lib/api";
import { <PERSON>, CardContent, CardHeader, <PERSON>T<PERSON><PERSON>, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import PageTransition from "@/components/animations/PageTransition";
import { motion } from "framer-motion";

const MotionButton = motion(Button);

export default function AdminLoginPage() {
  const router = useRouter();

  // If already authenticated, go straight to sessions.
  useEffect(() => {
    const token = typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;
    if (token) {
      router.replace("/admin/sessions");
    }
  }, [router]);
  const [username, setUsername] = useState("admin");
  const [password, setPassword] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      const { access_token } = await login(username, password);
      storeToken(access_token);
      router.push("/admin/sessions");
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <PageTransition>
      <div className="flex justify-center">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Admin Login</CardTitle>
        </CardHeader>
        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            <Input
              placeholder="Username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              required
            />
            <Input
              type="password"
              placeholder="Password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            {error && <p className="text-sm text-red-500">{error}</p>}
          </CardContent>
          <CardFooter className="justify-end">
            <MotionButton 
              type="submit" 
              disabled={loading}
              whileTap={{ scale: 0.97 }}
              transition={{ type: "spring", stiffness: 500, damping: 15 }}
            >
              {loading ? "Logging in..." : "Login"}
            </MotionButton>
          </CardFooter>
        </form>
      </Card>
    </div>
    </PageTransition>
  );
}
