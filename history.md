# Project History

---

## 2025-06-20

### Admin Dashboard Session Management Refinements & Theming
1.  **Backend Fix**: Resolved `AttributeError: 'InterviewSession' object has no attribute 'language'` by ensuring the `language` field was correctly handled in the session creation process (added to `schemas.py` and API logic if it was missing, or fixed type mismatches).
2.  **UI Theming**: Successfully applied Shadcn UI "New York" style with a blue color theme across the admin dashboard, enhancing visual consistency and appeal.
3.  **Session Creation UX Enhancements (`SessionsPage.tsx`):**
    *   **Automatic List Refresh**: Implemented `fetchSessions` call after successful session creation, ensuring the main sessions list updates immediately without requiring a manual page reload.
    *   **Persistent New Session Links**: Modified logic to keep candidate and interviewer URLs visible after creation until manually dismissed.
    *   **Styled Links Panel**: Replaced the basic link display with a visually distinct `Card` component (Shadcn UI).
        *   The card includes a title ("New Session Created!"), descriptive text, and success-themed styling (green accents).
        *   Links are displayed in read-only `Input` fields for easy copying, each with its own "Copy" button.
        *   Added a dismiss button (X icon) to the card, allowing users to hide the panel by calling `setLinks(null)`.
    *   **Component Fix**: Resolved a runtime error ("Element type is invalid... got: undefined") by correctly defining and exporting `CardDescription` from `@/components/ui/card.tsx` and importing it in `SessionsPage.tsx`.
4.  **Code Refinements**: Moved `fetchSessions` to component scope using `useCallback` for better reusability and to prevent stale closures.

> Use this file to keep a running log of design decisions, bugs, and fixes so that future sessions (or new developers) can quickly get up to speed.

---

## 2025-06-16

### Scope Recap
Building a **real-time collaborative coding interview platform** with rich telemetry for AI-powered cheating detection.

**Stack**: Next.js 15 (React 19) · FastAPI · Socket.IO · PostgreSQL · Redis · Judge0 · Docker Compose.

### Key Milestones Completed
1. 🏗️  Project structure, Docker Compose with frontend, backend, DB, Redis, Judge0.
2. 🗄️  SQLAlchemy models with UUID session IDs and telemetry tables.
3. 🔐  Admin login + session creation (candidate & interviewer tokens).
4. 🔌  WebSocket endpoints and React hook – code & keystroke sync now bi-directional.
5. ⚙️  Judge0 integration endpoint `/execute/python` (pending local sandbox issue on macOS).

### Recent Fixes
- Replaced deprecated `sio.asgi_app` with `socketio.ASGIApp` ➜ backend starts.
- Added UUID defaults in models; removed `MappedAsDataclass` ➜ DB inserts succeed.
- Fixed frontend API payloads & WS paths (`/ws/socket.io`).
- Synced event names (`code_update`, `keystroke_update`) between FE & BE.

### Current Blocking Issue
`judge0` container encounters **Internal Error – `/box/script.py` not found** on Apple Silicon due to missing cgroup v1. Works on native Linux/Intel.

**Next actions**
1. Developer switching to Windows laptop (Intel) to verify execution flow.
2. If sticking with Mac, spin up Colima (`colima start --arch x86_64`) to provide proper cgroups.
3. Once code execution stable, add DB indexing & UI polish.

## 2025-06-19

### UI/UX Polishing Sprint
1. Added colour palette + typography via Tailwind config and `@next/font/google`.
2. Created reusable `Badge` component (variants: default, secondary, success, warning, destructive, outline).
3. Updated navbar: new `<> Code Guardians` logo, theme toggle, logout button with icon + label.
4. Fixed routing: root `/` redirect, `/admin` redirect, client-side auth guarding.
5. Admin Dashboard overhaul:
   • Stats cards (Total / Active Sessions, Total Candidates) with Lucide icons and fade-in animation.
   • Card padding unified (`px-8 pt-10 pb-10`), grid centered (`max-w-6xl`).
   • Interview Sessions header left-aligned with subtitle (`text-neutral-500`) and right-aligned “+ New Session” button.
   • Logout moved from footer to navbar.
6. Aligned navbar inner container (`px-6 md:px-8`).
7. Removed obsolete Judge0 pieces from env/example.

---

## 2025-06-19 (earlier)

### Replaced Judge0 with go-judge
1. Added `go-judge` service to `docker-compose.yml`; removed Judge0.
2. Implemented `GoJudgeService` (Python & Java execution) and updated `/execute` endpoints.
3. Custom go-judge Dockerfile installs Python 3.12 & OpenJDK 17.
4. Frontend now supports language switcher (Python/Java) and shows runtime/compile errors in red.
5. Fixed CPU time overflow (ns ➜ ms) when persisting ExecutionEvents.
6. Updated documentation (README.md, plan.md).

---

*Keep appending to this file each time a significant change or discovery is made.*