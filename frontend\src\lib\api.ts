const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export interface LoginResponse {
  access_token: string;
  token_type: string;
}

export async function login(username: string, password: string) {
  const res = await fetch(`${API_URL}/api/v1/auth/login`, {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({ username, password }),
  });
  if (!res.ok) {
    throw new Error("Invalid credentials");
  }
  const data: LoginResponse = await res.json();
  return data;
}

export function storeToken(token: string) {
  if (typeof window !== "undefined") {
    localStorage.setItem("admin_token", token);
  }
}

export function removeToken() {
  if (typeof window !== "undefined") {
    localStorage.removeItem("admin_token");
  }
}

export function getAuthHeaders(): Record<string, string> {
  if (typeof window === "undefined") return {};
  const token = localStorage.getItem("admin_token");
  return token ? { Authorization: `Bearer ${token}` } : {};
}

export interface CreateSessionResponse {
  candidate_url: string;
  interviewer_url: string;
}

export interface SessionInfo {
  session_id: string;
  candidate_name: string;
  interviewer_name: string;
  job_title: string;
  candidate_url: string;
  interviewer_url: string;
  expires_at: string;
  created_at: string;
  is_active: boolean;
  language?: string; // Added field for programming language
}

export interface StatsResponse {
  total_sessions: number;
  active_sessions: number;
  total_candidates: number;
  success_rate: number;
}

export async function getSessions(): Promise<SessionInfo[]> {
  const res = await fetch(`${API_URL}/api/v1/sessions/`, { headers: getAuthHeaders() });
  if (!res.ok) throw new Error("unable to fetch sessions");
  return (await res.json()) as SessionInfo[];
}

export async function getStats(): Promise<StatsResponse> {
  try {
    const res = await fetch(`${API_URL}/api/v1/stats`, { headers: getAuthHeaders() });
    if (!res.ok) throw new Error("stats unavailable");
    return (await res.json()) as StatsResponse;
  } catch {
    // fallback dummy
    return {
      total_sessions: 0,
      active_sessions: 0,
      total_candidates: 0,
      success_rate: 0,
    };
  }
}

export interface NewSessionPayload {
  candidate_name: string;
  interviewer_name: string;
  job_title: string;
  language?: string; // Added field for programming language
  duration_minutes?: number;
}

export async function createSession(payload: NewSessionPayload) {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...getAuthHeaders(),
  };
  const res = await fetch(`${API_URL}/api/v1/sessions/`, {
    method: "POST",
    headers,
    body: JSON.stringify({
      candidate_name: payload.candidate_name,
      interviewer_name: payload.interviewer_name,
      job_title: payload.job_title,
      language: payload.language, // Added field
      duration_minutes: payload.duration_minutes ?? 120,
    }),
  });
  if (!res.ok) {
    throw new Error("Unable to create session");
  }
  const data: CreateSessionResponse = await res.json();
  return data;
}

export async function updateSessionStatus(sessionId: string, isActive: boolean): Promise<SessionInfo> {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    ...getAuthHeaders(),
  };
  const res = await fetch(`${API_URL}/api/v1/sessions/${sessionId}/status`,
    {
      method: "PATCH",
      headers,
      body: JSON.stringify({ is_active: isActive }),
    }
  );
  if (!res.ok) {
    const errorData = await res.json().catch(() => ({ detail: "Failed to update session status" }));
    throw new Error(errorData.detail || "Failed to update session status");
  }
  return (await res.json()) as SessionInfo;
}

export async function deleteSession(sessionId: string): Promise<void> {
  const headers: Record<string, string> = {
    ...getAuthHeaders(),
  };
  const res = await fetch(`${API_URL}/api/v1/sessions/${sessionId}`,
    {
      method: "DELETE",
      headers,
    }
  );
  if (!res.ok) { // Handles 404 for not found, or other server errors
    const errorData = await res.json().catch(() => ({ detail: "Failed to delete session" }));
    throw new Error(errorData.detail || "Failed to delete session");
  }
  // For 204 No Content, there's no body to parse, so we don't call res.json()
}
