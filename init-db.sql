-- Initial database schema for Code Guardians POC
-- Extensions
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON><PERSON>
CREATE TABLE IF NOT EXISTS admins (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    hashed_password VARCHAR(128) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Interview sessions
CREATE TABLE IF NOT EXISTS interview_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    admin_id INTEGER NOT NULL,
    candidate_name VARCHAR(100) NOT NULL,
    interviewer_name VARCHAR(100) NOT NULL,
    job_title VARCHAR(100) NOT NULL,
    language VARCHAR(50),
    candidate_token VARCHAR(255) UNIQUE NOT NULL,
    interviewer_token VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE
);

-- Keystroke events
CREATE TABLE IF NOT EXISTS keystroke_events (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES interview_sessions(id) ON DELETE CASCADE,
    user_type VARCHAR(20) NOT NULL,
    event_type VARCHAR(10) NOT NULL,
    key_code INTEGER NOT NULL,
    key_char VARCHAR(10),
    timestamp_ms BIGINT NOT NULL,
    inter_key_interval INTEGER,
    cursor_position JSONB,
    selection_range JSONB,
    modifiers JSONB,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Code changes
CREATE TABLE IF NOT EXISTS code_changes (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES interview_sessions(id) ON DELETE CASCADE,
    user_type VARCHAR(20) NOT NULL,
    change_type VARCHAR(20) NOT NULL,
    content TEXT,
    position_start JSONB,
    position_end JSONB,
    timestamp_ms BIGINT NOT NULL,
    full_code_snapshot TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Interview artifacts (questions, expected outputs)
CREATE TABLE IF NOT EXISTS interview_artifacts (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES interview_sessions(id) ON DELETE CASCADE,
    artifact_type VARCHAR(50) NOT NULL, -- 'question' or 'expected_output'
    content TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Code execution events
CREATE TABLE IF NOT EXISTS execution_events (
    id BIGSERIAL PRIMARY KEY,
    session_id UUID REFERENCES interview_sessions(id) ON DELETE CASCADE,
    artifact_id BIGINT REFERENCES interview_artifacts(id), -- Optional link to a question
    user_type VARCHAR(20) NOT NULL,
    code_snapshot TEXT NOT NULL,
    execution_start_ms BIGINT NOT NULL,
    execution_end_ms BIGINT,
    output TEXT,
    error_message TEXT,
    memory_usage INTEGER,
    cpu_time_ms INTEGER,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
