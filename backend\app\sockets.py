"""Socket.io server and event handlers."""
from datetime import datetime
from typing import Any, Dict
from uuid import UUID

import socketio
from sqlalchemy import update
from sqlalchemy.ext.asyncio import AsyncSession

from .core.db import async_session
from .models.schemas import CodeChange, ExecutionEvent, InterviewSession, KeystrokeEvent

sio = socketio.AsyncServer(async_mode="asgi", cors_allowed_origins="*")


def background_session():
    """Yield a new session per call."""
    return async_session()


@sio.event
async def connect(sid, environ):  # noqa: D401
    print(f"[socket] connect {sid}")


@sio.event
async def authenticate(sid, environ):  # noqa: D401
    # reuse connect logic for optional authenticate event
    await connect(sid, environ)


@sio.event
async def disconnect(sid):  # noqa: D401
    print(f"[socket] disconnect {sid}")


@sio.event
async def join_session(sid, data: Dict[str, Any]):  # noqa: D401
    """Client requests to join a session room.

    Expected data: { "session_id": str }
    """
    session_id = data.get("session_id")
    if session_id is None:
        return
    room = f"session_{session_id}"
    await sio.enter_room(sid, room)
    print(f"[socket] {sid} joined {room}")


@sio.event
async def code_change(sid, data: Dict[str, Any]):  # noqa: D401
    """Persist code change and broadcast to room."""
    room = f"session_{data.get('sessionId')}"
    await sio.emit("code_update", data, room=room, skip_sid=sid)

    # Persist asynchronously
    async with background_session() as db:  # type: AsyncSession
        db.add(CodeChange(
            session_id=data.get("sessionId"),
            user_type=data.get("userType"),
            change_type=data.get("changeType"),
            content=data.get("content"),
            position_start=data.get("positionStart"),
            position_end=data.get("positionEnd"),
            timestamp_ms=data.get("timestampMs"),
            full_code_snapshot=data.get("fullCodeSnapshot"),
            created_at=datetime.utcnow(),
        ))
        await db.commit()


@sio.event
async def keystroke(sid, data):  # noqa: D401
    room = f"session_{data.get('sessionId')}"
    await sio.emit("keystroke_update", data, room=room, skip_sid=sid)
    async with background_session() as db:
        db.add(KeystrokeEvent(
            session_id=data.get("sessionId"),
            user_type=data.get("userType"),
            event_type=data.get("eventType"),
            key_code=data.get("keyCode"),
            key_char=data.get("keyChar"),
            timestamp_ms=data.get("timestampMs"),
            inter_key_interval=data.get("interKeyInterval"),
            cursor_position=data.get("cursorPosition"),
            selection_range=data.get("selectionRange"),
            modifiers=data.get("modifiers"),
            created_at=datetime.utcnow(),
        ))
        await db.commit()


@sio.event
async def language_change(sid, data: Dict[str, Any]):
    """Update session language and broadcast to room."""
    session_id_str = data.get("sessionId")
    new_language = data.get("language")
    if not session_id_str or not new_language:
        print(f"[socket] language_change event missing data: {data}")
        return

    room = f"session_{session_id_str}"

    try:
        session_uuid = UUID(session_id_str)
        async with background_session() as db:  # type: AsyncSession
            stmt = (
                update(InterviewSession)
                .where(InterviewSession.id == session_uuid)
                .values(language=new_language)
            )
            result = await db.execute(stmt)
            
            if result.rowcount == 0:
                print(f"[socket] language_change: No session found with ID {session_id_str}")
                return

            await db.commit()

        await sio.emit("language_update", {"language": new_language}, room=room)
        print(f"[socket] language for {room} updated to {new_language}")

    except Exception as e:
        print(f"[socket] Error updating language for session {session_id_str}: {e}")


@sio.event
async def execution_result(sid, data):  # noqa: D401
    room = f"session_{data.get('sessionId')}"
    await sio.emit("execution_result", data, room=room, skip_sid=sid)
    async with background_session() as db:
        db.add(ExecutionEvent(
            session_id=data.get("sessionId"),
            user_type=data.get("userType"),
            code_snapshot=data.get("codeSnapshot"),
            execution_start_ms=data.get("executionStartMs"),
            execution_end_ms=data.get("executionEndMs"),
            output=data.get("output"),
            error_message=data.get("error"),
            memory_usage=data.get("memoryUsage"),
            cpu_time_ms=data.get("cpuTimeMs"),
            created_at=datetime.utcnow(),
        ))
        await db.commit()
