#!/usr/bin/env python3
"""
Test script to verify logging implementation works correctly.
Run this script to test various logging scenarios and ensure the logging system is working.
"""

import asyncio
import os
import sys
import tempfile
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from app.core.logging import configure_logging, get_logger, LogContext, PerformanceLogger


async def test_basic_logging():
    """Test basic logging functionality."""
    print("=== Testing Basic Logging ===")
    
    # Configure logging
    configure_logging()
    
    # Test different loggers
    main_logger = get_logger("test.main")
    db_logger = get_logger("test.database")
    api_logger = get_logger("test.api")
    
    # Test different log levels
    main_logger.debug("This is a debug message", component="test")
    main_logger.info("This is an info message", component="test")
    main_logger.warning("This is a warning message", component="test")
    main_logger.error("This is an error message", component="test")
    
    # Test structured logging with context
    main_logger.info("User action", 
                    user_id=123, 
                    action="login", 
                    ip_address="***********")
    
    db_logger.info("Database query", 
                  query="SELECT * FROM users", 
                  duration_ms=45.2)
    
    api_logger.info("API request", 
                   method="POST", 
                   endpoint="/api/sessions", 
                   status_code=201)
    
    print("✓ Basic logging test completed")


async def test_log_context():
    """Test LogContext helper functionality."""
    print("\n=== Testing LogContext ===")

    logger = get_logger("test.context")

    # Test different context types
    session_ctx = LogContext.session("session_123", "candidate")
    logger.info("Session created", **session_ctx)

    request_ctx = LogContext.request("POST", "/api/test", "req_456")
    logger.info("HTTP request received", **request_ctx)

    db_ctx = LogContext.database("INSERT", "sessions")
    logger.info("Database operation", **db_ctx)

    ws_ctx = LogContext.websocket("connect", "session_123")
    logger.info("WebSocket event", **ws_ctx)

    ai_ctx = LogContext.ai_analysis("session_456", "code_analysis")
    logger.info("AI analysis started", **ai_ctx)

    exec_ctx = LogContext.code_execution("session_789", "python")
    logger.info("Code execution started", **exec_ctx)

    print("✓ LogContext test completed")


async def test_performance_logger():
    """Test PerformanceLogger functionality."""
    print("\n=== Testing PerformanceLogger ===")
    
    logger = get_logger("test.performance")
    
    # Test performance logging with context manager
    with PerformanceLogger(logger, "test_operation", operation_type="test"):
        # Simulate some work
        await asyncio.sleep(0.1)
    
    # Test performance logging with manual timing
    perf_logger = PerformanceLogger(logger, "manual_test", user_id=123)
    perf_logger.start()
    await asyncio.sleep(0.05)
    perf_logger.end()
    
    print("✓ PerformanceLogger test completed")


async def test_error_handling():
    """Test error logging and exception handling."""
    print("\n=== Testing Error Handling ===")
    
    logger = get_logger("test.errors")
    
    try:
        # Simulate an error
        raise ValueError("This is a test error")
    except Exception as e:
        logger.error("Test error occurred", 
                    error=str(e), 
                    error_type=type(e).__name__)
    
    # Test logging with exception info
    try:
        result = 1 / 0
    except ZeroDivisionError:
        logger.error("Division by zero error", exc_info=True)
    
    print("✓ Error handling test completed")


def test_file_logging():
    """Test file logging configuration."""
    print("\n=== Testing File Logging ===")
    
    # Set environment variables for file logging
    with tempfile.TemporaryDirectory() as temp_dir:
        os.environ["ENABLE_FILE_LOGGING"] = "true"
        os.environ["LOG_DIR"] = temp_dir
        
        # Reconfigure logging with file output
        configure_logging()
        
        logger = get_logger("test.file")
        logger.info("This message should go to file", test_data="file_logging")
        
        # Check if log file was created
        log_file = Path(temp_dir) / "code_guardians.log"
        if log_file.exists():
            print(f"✓ Log file created: {log_file}")
            with open(log_file, 'r') as f:
                content = f.read()
                if "This message should go to file" in content:
                    print("✓ Log content written to file successfully")
                else:
                    print("✗ Log content not found in file")
        else:
            print("✗ Log file was not created")
    
    # Clean up environment
    os.environ.pop("ENABLE_FILE_LOGGING", None)
    os.environ.pop("LOG_DIR", None)


async def test_import_logging_modules():
    """Test that logging modules can be imported without errors."""
    print("\n=== Testing Logging Module Imports ===")

    try:
        from app.core.logging import configure_logging, get_logger, LogContext, PerformanceLogger
        print("✓ Core logging module imported successfully")

        # Test basic logging configuration
        logger = get_logger("test.import")
        logger.info("Import test successful")
        print("✓ Logger creation and basic usage works")

        return True
    except Exception as e:
        print(f"✗ Logging module import failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """Run all logging tests."""
    print("Starting Code Guardians Logging System Tests")
    print("=" * 50)

    # Test module imports first
    if not await test_import_logging_modules():
        print("\n❌ Logging module import tests failed. Please check for syntax errors.")
        return False

    # Run logging tests
    try:
        await test_basic_logging()
        await test_log_context()
        await test_performance_logger()
        await test_error_handling()
        test_file_logging()

        print("\n" + "=" * 50)
        print("🎉 All logging tests completed successfully!")
        print("\nLogging system is ready for production use.")
        print("\nNext steps:")
        print("1. Start the application with docker-compose up")
        print("2. Check logs in the console and /app/logs/ directory")
        print("3. Test API endpoints to verify request/response logging")
        print("4. Monitor log rotation and file sizes")
        print("\nNote: Full application module tests require FastAPI and other dependencies.")
        print("The core logging functionality has been verified and is working correctly.")

        return True

    except Exception as e:
        print(f"\n❌ Logging tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
