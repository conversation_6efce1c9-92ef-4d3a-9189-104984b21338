"""FastAPI entrypoint."""
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from .routers import auth, sessions, execute, stats, analysis
from .routers.auth import ensure_default_admin

import socketio
from .sockets import sio

app = FastAPI(title="Code Guardians API")

# Mount Socket.IO ASGI app at /ws
app.mount("/ws", socketio.ASGIApp(sio, socketio_path=""))

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/v1")
app.include_router(sessions.router, prefix="/api/v1")

# Create and include the analysis router, passing the sio instance
analysis_router = analysis.create_analysis_router(sio)
app.include_router(analysis_router, prefix="/api/v1")

app.include_router(execute.router, prefix="/api/v1")
app.include_router(stats.router, prefix="/api/v1")


@app.on_event("startup")
async def startup_event():
    # create default admin if none exist
    await ensure_default_admin()


@app.get("/health", tags=["system"])
async def health():
    return {"status": "ok"}
