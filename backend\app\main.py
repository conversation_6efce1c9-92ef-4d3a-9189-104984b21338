"""FastAPI entrypoint."""
import os
from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware

from .core.logging import configure_logging, get_logger
from .middleware.logging import LoggingMiddleware
from .routers import auth, sessions, execute, stats, analysis
from .routers.auth import ensure_default_admin

import socketio
from .sockets import sio

# Initialize logging first
logger = configure_logging()

app = FastAPI(title="Code Guardians API")

# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Mount Socket.IO ASGI app at /ws
app.mount("/ws", socketio.ASGIApp(sio, socketio_path=""))

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

app.include_router(auth.router, prefix="/api/v1")
app.include_router(sessions.router, prefix="/api/v1")

# Create and include the analysis router, passing the sio instance
analysis_router = analysis.create_analysis_router(sio)
app.include_router(analysis_router, prefix="/api/v1")

app.include_router(execute.router, prefix="/api/v1")
app.include_router(stats.router, prefix="/api/v1")


@app.on_event("startup")
async def startup_event():
    """Application startup event."""
    logger.info(
        "Starting Code Guardians API",
        environment=os.getenv("ENVIRONMENT", "development"),
        log_level=os.getenv("LOG_LEVEL", "INFO")
    )

    # create default admin if none exist
    try:
        await ensure_default_admin()
        logger.info("Default admin initialization completed")
    except Exception as e:
        logger.error("Failed to initialize default admin", error=str(e))
        raise


@app.on_event("shutdown")
async def shutdown_event():
    """Application shutdown event."""
    logger.info("Shutting down Code Guardians API")


@app.get("/health", tags=["system"])
async def health():
    """Health check endpoint."""
    logger.debug("Health check requested")
    return {"status": "ok"}
