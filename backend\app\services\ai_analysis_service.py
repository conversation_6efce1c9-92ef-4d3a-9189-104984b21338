import os
from typing import TypedDict, <PERSON><PERSON><PERSON>, <PERSON>, Union

from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
import mlflow
from langchain_core.output_parsers import JsonOutputParser
from dotenv import load_dotenv
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
import tempfile
import uuid

from app.core.logging import get_logger, LogContext, PerformanceLogger

# Load environment variables from .env file
load_dotenv()

# Initialize logging
logger = get_logger("ai_analysis")

# MLflow and LangChain Tracing Setup
MLFLOW_TRACKING_URI = os.getenv("MLFLOW_TRACKING_URI")
if MLFLOW_TRACKING_URI:
    mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
    mlflow.langchain.autolog()

# 1. Define the State for our graph
class GraphState(TypedDict):
    """
    Represents the state of our graph.

    Attributes:
        question: The interview question.
        candidate_code: The code submitted by the candidate.
        generated_solutions: A list of potential solutions generated by an AI model.
        initial_analysis: The analysis from the first, faster AI model.
        is_uncertain: A flag indicating if the initial analysis was inconclusive.
        final_verdict: The final verdict from the more powerful AI model, if invoked.
        error: An error message if something goes wrong.
    """
    question: str
    candidate_code: str
    generated_solutions: Optional[List[str]] = None
    analysis_details: Optional[dict] = None
    is_uncertain: Optional[bool] = None
    final_verdict: Optional[dict] = None
    error: Optional[str] = None

# 2. Define Pydantic models for structured output
class SolutionItem(BaseModel):
    """Data model for a single solution item."""
    name: str = Field(description="Name/title of the solution approach")
    description: str = Field(description="Brief description of the solution approach")
    code: str = Field(description="The actual code implementation")

class GeneratedSolutions(BaseModel):
    """Data model for the generated solutions."""
    solutions: List[SolutionItem] = Field(description="A list of 2-3 distinct and valid code solutions for the given problem.")

class InitialAnalysis(BaseModel):
    """Data model for the initial analysis of the candidate's code."""
    is_similar: bool = Field(description="Whether the candidate's code is substantially similar to any of the generated solutions.")
    explanation: str = Field(description="A brief explanation for the verdict, highlighting similarities or differences.")
    certainty_score: float = Field(description="A score from 0.0 to 1.0 indicating the model's confidence in its verdict.")

class FinalVerdict(BaseModel):
    """Data model for the final, expert verdict on the candidate's code."""
    cheating_probability: float = Field(description="A probability score from 0.0 to 1.0 indicating the likelihood of cheating.")
    reasoning: str = Field(description="A detailed reasoning for the final verdict, considering all evidence.")

class AnalysisPayload(BaseModel):
    """Data model for the input to the analysis graph."""
    question: str
    candidate_code: str
    session_id: str

# 3. Set up the models
LLM_PROVIDER = os.getenv("LLM_PROVIDER", "google").lower()
MODEL_MAX_RETRIES = int(os.getenv("MODEL_MAX_RETRIES", "2"))

def get_chat_model(model_name: str, temperature: float):
    """Factory function to get the appropriate chat model based on the provider."""
    model = None
    if LLM_PROVIDER == "openrouter":
        logger.info("Initializing OpenRouter model",
                   model_name=model_name,
                   temperature=temperature,
                   provider="openrouter")
        model = ChatOpenAI(
            model=model_name,
            temperature=temperature,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url=os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
        )
    else:  # Default to Google
        logger.info("Initializing Google model",
                   model_name=model_name,
                   temperature=temperature,
                   provider="google")
        model = ChatGoogleGenerativeAI(model=model_name, temperature=temperature, google_api_key=os.getenv("GOOGLE_API_KEY"))
    return model

# Model names are sourced from environment variables with sensible defaults.
SOLUTION_MODEL_NAME = os.getenv("SOLUTION_MODEL", "google/gemini-2.5-flash-lite-preview-06-17")
ANALYSIS_MODEL_NAME = os.getenv("ANALYSIS_MODEL", "google/gemini-2.5-flash")
EXPERT_MODEL_NAME = os.getenv("EXPERT_MODEL", "google/gemini-2.5-pro")

solution_model = get_chat_model(SOLUTION_MODEL_NAME, temperature=0.3)
analysis_model = get_chat_model(ANALYSIS_MODEL_NAME, temperature=0)
expert_model = get_chat_model(EXPERT_MODEL_NAME, temperature=0)

# 4. Implement Agent Nodes
def solution_generation_node(state: GraphState):
    """
    Generates potential solutions for the given interview question.
    """
    logger.info("Starting solution generation node",
               component="ai_analysis",
               analysis_stage="solution_generation")
    question = state["question"]

    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior software engineer. Your task is to generate 2-3 distinct and valid code solutions for the given programming problem. Respond with a JSON object containing a 'solutions' key, which is a list of objects. Each object should have 'name' (solution approach name), 'description' (brief explanation), and 'code' (the actual implementation) fields."),
        ("user", "{question}")
    ])

    parser = JsonOutputParser()
    chain = prompt | solution_model | parser
    chain_with_retry = chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    with PerformanceLogger(logger, "solution_generation",
                          component="ai_analysis",
                          model_name=SOLUTION_MODEL_NAME,
                          max_retries=MODEL_MAX_RETRIES):
        try:
            logger.debug("Invoking solution generation chain",
                        max_retries=MODEL_MAX_RETRIES,
                        model_name=SOLUTION_MODEL_NAME)
            result_dict = chain_with_retry.invoke({"question": question})
            parsed_output = GeneratedSolutions.parse_obj(result_dict)

            # Extract just the code from each solution for downstream processing
            solution_codes = [solution.code for solution in parsed_output.solutions]

            logger.info("Solution generation completed successfully",
                       solutions_count=len(parsed_output.solutions),
                       solution_names=[sol.name for sol in parsed_output.solutions],
                       component="ai_analysis")
            return {"generated_solutions": solution_codes}
        except Exception as e:
            logger.error("Solution generation failed",
                        error=str(e),
                        error_type=type(e).__name__,
                        component="ai_analysis")
            return {"error": "Failed to generate solutions."}

def initial_analysis_node(state: GraphState):
    """
    Performs an initial analysis of the candidate's code against the generated solutions.
    """
    logger.info("Starting initial analysis node",
               component="ai_analysis",
               analysis_stage="initial_analysis")
    question = state["question"]
    candidate_code = state["candidate_code"]
    generated_solutions = state["generated_solutions"]

    prompt = ChatPromptTemplate.from_messages([
    ("system",
     # --- NEW SYSTEM PROMPT ---
     (
         "You are an automated plagiarism-detection engine for live coding interviews.\n"
         "Goal: decide whether the candidate’s solution was likely **copied** from one of the "
         "AI-generated reference solutions.\n\n"
         "Decision rules:\n"
         "1. **Strong-evidence test** – Mark `is_similar: true` **only** if BOTH conditions hold:\n"
         "   • The core algorithmic steps are the same **and**\n"
         "   • You detect at least two of these uncommon coincidences:\n"
         "     – identical variable / function names\n"
         "     – identical comment strings or docstrings\n"
         "     – identical control-flow ordering and edge-case handling style\n"
         "     – identical spacing / layout patterns (beyond standard PEP-8)\n"
         "2. **Standard-textbook rule** – If the task admits only a handful of obvious solutions "
         "(e.g., finding max in a list, Fibonacci, string reversal) **do not** treat matching "
         "logic alone as plagiarism.  Unless Rule 1’s extra coincidences are present, set "
         "`is_similar: false` and keep `certainty_score ≤ 0.30`.\n"
         "3. Confidence scale: > 0.8  = strong evidence, 0.4–0.8  = moderate, < 0.4 = weak.\n\n"
         "Return **only** a JSON object with:\n"
         "  • `is_similar`  (boolean)\n"
         "  • `explanation` (brief ≤ 40 words)\n"
         "  • `certainty_score` (0.0-1.0)\n"
     )
    ),
    ("user",
     "Problem Question:\n{question}\n\nAI-Generated Solutions:\n{solutions}\n\nCandidate's Code:\n{code}")
    ])

    analysis_parser = JsonOutputParser()
    analysis_chain = prompt | analysis_model | analysis_parser
    analysis_chain = analysis_chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    with PerformanceLogger(logger, "initial_analysis",
                          component="ai_analysis",
                          model_name=ANALYSIS_MODEL_NAME,
                          max_retries=MODEL_MAX_RETRIES):
        try:
            logger.debug("Invoking initial analysis chain",
                        max_retries=MODEL_MAX_RETRIES,
                        model_name=ANALYSIS_MODEL_NAME,
                        solutions_count=len(generated_solutions),
                        code_length=len(candidate_code))
            result_dict = analysis_chain.invoke({
                "question": question,
                "solutions": "\n---\n".join(generated_solutions),
                "code": candidate_code
            })
            parsed_output = InitialAnalysis.parse_obj(result_dict)
            is_plagiarism_claimed = parsed_output.is_similar          # only escalate if plagiarism is alleged
certainty            = parsed_output.certainty_score

LOW_CONF_THRESHOLD   = 0.40   # escalate band lower bound
ESCALATE_THRESHOLD   = 0.90   # existing upper bound

is_uncertain = (
    is_plagiarism_claimed and
    LOW_CONF_THRESHOLD <= certainty < ESCALATE_THRESHOLD
)
            logger.info("Initial analysis completed",
                       certainty_score=parsed_output.certainty_score,
                       is_similar=parsed_output.is_similar,
                       is_uncertain=is_uncertain,
                       component="ai_analysis")
            return {
                "analysis_details": parsed_output.dict(),
                "is_uncertain": is_uncertain,
            }
        except Exception as e:
            logger.error("Initial analysis failed",
                        error=str(e),
                        error_type=type(e).__name__,
                        component="ai_analysis")
            return {"error": "Failed to perform initial analysis."}

def expert_analysis_node(state: GraphState):
    """
    Performs a more detailed analysis using a powerful model when the initial verdict is uncertain.
    """
    logger.info("Starting expert analysis node",
               component="ai_analysis",
               analysis_stage="expert_analysis")
    question = state["question"]
    candidate_code = state["candidate_code"]
    generated_solutions = state["generated_solutions"]
    initial_analysis_text = state["analysis_details"]["explanation"]

    prompt = ChatPromptTemplate.from_messages([
    ("system",
     # --- NEW SYSTEM PROMPT ---
     (
         "You are an academic-integrity adjudication engine.  Issue a final verdict on whether the "
         "candidate’s code is plagiarized.\n"
         "• Write in a neutral, third-person ‘report’ style (no first-person or role references).\n"
         "• Begin `reasoning` with a **2–3 sentence executive summary** (≤ 60 words) stating the "
         "verdict and probability.\n"
         "• Follow with concise bullet-style points (numbered or dashed) giving key evidence for and "
         "against plagiarism, especially noting if the problem is a standard textbook exercise.\n\n"
         "Respond with JSON:\n"
         "  • `cheating_probability` (0.0-1.0)\n"
         "  • `reasoning`            (the report)"
     )
    ),
    ("user",
     "Problem Question:\n{question}\n\nAI-Generated Solutions:\n{solutions}\n\nCandidate's Code:\n{code}\n\n"
     "Initial (Uncertain) Analysis:\n{initial_analysis}")
    ])

    expert_parser = JsonOutputParser()
    expert_chain = prompt | expert_model | expert_parser
    expert_chain = expert_chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    with PerformanceLogger(logger, "expert_analysis",
                          component="ai_analysis",
                          model_name=EXPERT_MODEL_NAME,
                          max_retries=MODEL_MAX_RETRIES):
        try:
            logger.debug("Invoking expert analysis chain",
                        max_retries=MODEL_MAX_RETRIES,
                        model_name=EXPERT_MODEL_NAME,
                        solutions_count=len(generated_solutions),
                        code_length=len(candidate_code))
            result_dict = expert_chain.invoke({
                "question": question,
                "solutions": "\n---\n".join(generated_solutions),
                "code": candidate_code,
                "initial_analysis": initial_analysis_text
            })
            parsed_output = FinalVerdict.parse_obj(result_dict)
            logger.info("Expert analysis completed",
                       cheating_probability=parsed_output.cheating_probability,
                       component="ai_analysis")
            return {"final_verdict": parsed_output.dict()}
        except Exception as e:
            logger.error("Expert analysis failed",
                        error=str(e),
                        error_type=type(e).__name__,
                        component="ai_analysis")
            return {"error": "Failed to perform expert analysis."}

def should_continue_to_expert(state: GraphState):
    """
    Determines whether to proceed to the expert analysis node or end the workflow.
    """
    logger.debug("Evaluating conditional edge: should continue to expert?",
                component="ai_analysis")
    if state.get("error"):
        logger.warning("Error detected in analysis state, ending workflow",
                      error=state.get("error"),
                      component="ai_analysis")
        return "end"
    is_uncertain = state.get("is_uncertain", False)
    if is_uncertain:
        logger.info("Initial analysis uncertain, escalating to expert model",
                   component="ai_analysis",
                   analysis_stage="escalation")
        return "expert_analysis"
    else:
        logger.info("Initial analysis certain, completing workflow",
                   component="ai_analysis",
                   analysis_stage="completion")
        return "end"

# 6. Assemble the graph
from langgraph.graph import StateGraph, END

workflow = StateGraph(GraphState)
workflow.add_node("solution_generation", solution_generation_node)
workflow.add_node("initial_analysis", initial_analysis_node)
workflow.add_node("expert_analysis", expert_analysis_node)
workflow.set_entry_point("solution_generation")
workflow.add_edge("solution_generation", "initial_analysis")
workflow.add_conditional_edges(
    "initial_analysis",
    should_continue_to_expert,
    {
        "expert_analysis": "expert_analysis",
        "end": END
    }
)
workflow.add_edge("expert_analysis", END)
app = workflow.compile()

async def stream_ai_analysis(db: AsyncSession, payload: AnalysisPayload, sio_instance):
    """
    Runs the full AI analysis, streaming progress updates via WebSockets and logging
    the final results to MLflow.
    """
    # Create analysis context for consistent logging
    analysis_ctx = LogContext.ai_analysis(payload.session_id, "stream_analysis")

    logger.info("Starting AI analysis stream",
               question_length=len(payload.question),
               code_length=len(payload.candidate_code),
               **analysis_ctx)

    room = f"session_{payload.session_id}"
    node_to_message = {
        "solution_generation": "Generating potential solutions...",
        "initial_analysis": "Performing initial analysis...",
        "expert_analysis": "Initial analysis uncertain. Invoking expert model...",
    }

    # --- Step 1: Execute the graph within an MLflow run context ---
    experiment_name = f"Session_{payload.session_id}"
    mlflow.set_experiment(experiment_name)

    with PerformanceLogger(logger, "full_ai_analysis", **analysis_ctx):
        with mlflow.start_run() as run:
            logger.info("MLflow run started",
                       run_id=run.info.run_id,
                       experiment_name=experiment_name,
                       **analysis_ctx)
            run_id = run.info.run_id
            inputs = {"question": payload.question, "candidate_code": payload.candidate_code}
            config = {"configurable": {"thread_id": run_id}}
            final_state = None

            try:
                logger.info("Starting AI analysis graph streaming", **analysis_ctx)
                async for event in app.astream_events(inputs, config, version="v1"):
                    kind = event["event"]
                    if kind == "on_chain_start":
                        node_name = event["name"]
                        if node_name in node_to_message:
                            message = node_to_message[node_name]
                            logger.debug("Analysis progress update",
                                       node_name=node_name,
                                       message=message,
                                       **analysis_ctx)
                            await sio_instance.emit("analysis_progress", {"message": message}, room=room)
                    elif kind == "on_chain_end":
                        if event["name"] == "LangGraph":  # The root graph is named "LangGraph" by default
                            final_state = event["data"]["output"]
                            logger.debug("Graph execution completed", **analysis_ctx)
                logger.info("AI analysis graph streaming completed successfully", **analysis_ctx)
            except Exception as e:
                logger.error("Graph execution failed",
                           error=str(e),
                           error_type=type(e).__name__,
                           **analysis_ctx)
                final_state = {"error": str(e)}
                mlflow.log_param("error", str(e))

            # --- Step 2: Determine final verdict ---
            logger.info("Processing analysis results to determine final verdict", **analysis_ctx)
            final_verdict = None
            if isinstance(final_state, dict):
                expert_analysis_result = final_state.get("expert_analysis")
                if expert_analysis_result and expert_analysis_result.get("final_verdict"):
                    # Expert analysis was performed – use its verdict
                    expert_verdict = expert_analysis_result["final_verdict"]
                    probability = expert_verdict.get("cheating_probability", 0.0)
                    if probability > 0.75:
                        verdict_str = "High-Confidence Match"
                    elif probability > 0.4:
                        verdict_str = "Potential Match"
                    else:
                        verdict_str = "No Cheating Detected"
                    final_verdict = {
                        "verdict": verdict_str,
                        "explanation": expert_verdict.get("reasoning", "N/A"),
                        "certainty_score": 1.0,  # Expert model is considered fully certain in its conclusion
                        "cheating_probability": probability,
                    }
                    logger.info("Final verdict determined from expert analysis",
                               verdict=verdict_str,
                               cheating_probability=probability,
                               **analysis_ctx)
                elif final_state.get("initial_analysis"):
                    # No expert step, so use the initial analysis outcome
                    initial_analysis_result = final_state.get("initial_analysis", {})
                    analysis_details = initial_analysis_result.get("analysis_details")
                    if analysis_details:
                        is_similar = analysis_details.get("is_similar", False)
                        # Use the initial model's certainty to set cheating probability instead of a binary value
                        probability = analysis_details.get("certainty_score", 0.0) if is_similar else 1.0 - analysis_details.get("certainty_score", 0.0)
                        if probability > 0.75:
                            verdict_str = "High-Confidence Match"
                        elif probability > 0.4:
                            verdict_str = "Potential Match"
                        else:
                            verdict_str = "No Cheating Detected"
                        final_verdict = {
                            "verdict": verdict_str,
                            "explanation": analysis_details.get("explanation", "N/A"),
                            "certainty_score": analysis_details.get("certainty_score", 0.0),
                            "cheating_probability": probability,
                        }
                        logger.info("Final verdict determined from initial analysis",
                                   verdict=verdict_str,
                                   cheating_probability=probability,
                                   certainty_score=analysis_details.get("certainty_score", 0.0),
                                   is_similar=is_similar,
                                   **analysis_ctx)

            # --- Step 3: Log params, metrics, and artifacts ---
            logger.info("Logging analysis results to MLflow", **analysis_ctx)
            mlflow.log_param("question", payload.question)
            mlflow.log_param("llm_provider", LLM_PROVIDER)
            if final_verdict:
                cheating_prob = final_verdict.get("cheating_probability", 0.0)
                mlflow.log_metric("cheating_probability", cheating_prob)

                # Log candidate code as artifact
                with tempfile.NamedTemporaryFile("w", delete=False, suffix=".txt", encoding="utf-8") as tmp:
                    tmp.write(payload.candidate_code)
                    mlflow.log_artifact(tmp.name, "candidate_code.txt")
                os.unlink(tmp.name)

                # Log explanation as artifact
                with tempfile.NamedTemporaryFile("w", delete=False, suffix=".txt", encoding="utf-8") as tmp:
                    tmp.write(final_verdict.get("explanation", "N/A"))
                    mlflow.log_artifact(tmp.name, "explanation.txt")
                os.unlink(tmp.name)

                logger.info("MLflow artifacts logged successfully",
                           cheating_probability=cheating_prob,
                           **analysis_ctx)
            else:
                logger.warning("No final verdict available for MLflow logging", **analysis_ctx)

            logger.info("MLflow run completed successfully", **analysis_ctx)

            # --- Step 4: Broadcast final results to the client ---
            logger.info("Broadcasting analysis results via WebSocket",
                       room=room,
                       has_final_verdict=final_verdict is not None,
                       **analysis_ctx)
            await sio_instance.emit("analysis_complete", {"final_verdict": final_verdict}, room=room)
            logger.info("Analysis results broadcast completed successfully",
                       room=room,
                       **analysis_ctx)
