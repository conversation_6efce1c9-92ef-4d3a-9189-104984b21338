import os
from typing import TypedDict, Annota<PERSON>, <PERSON>, Union

from pydantic import BaseModel, Field
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_openai import ChatOpenAI
from langchain_core.prompts import ChatPromptTemplate
import mlflow
from langchain_core.output_parsers import JsonOutputParser
from dotenv import load_dotenv
from typing import Optional
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
import tempfile
import uuid

# Load environment variables from .env file
load_dotenv()

# MLflow and LangChain Tracing Setup
MLFLOW_TRACKING_URI = os.getenv("MLFLOW_TRACKING_URI")
if MLFLOW_TRACKING_URI:
    mlflow.set_tracking_uri(MLFLOW_TRACKING_URI)
    mlflow.langchain.autolog()

# 1. Define the State for our graph
class GraphState(TypedDict):
    """
    Represents the state of our graph.

    Attributes:
        question: The interview question.
        candidate_code: The code submitted by the candidate.
        generated_solutions: A list of potential solutions generated by an AI model.
        initial_analysis: The analysis from the first, faster AI model.
        is_uncertain: A flag indicating if the initial analysis was inconclusive.
        final_verdict: The final verdict from the more powerful AI model, if invoked.
        error: An error message if something goes wrong.
    """
    question: str
    candidate_code: str
    generated_solutions: Optional[List[str]] = None
    analysis_details: Optional[dict] = None
    is_uncertain: Optional[bool] = None
    final_verdict: Optional[dict] = None
    error: Optional[str] = None

# 2. Define Pydantic models for structured output
class GeneratedSolutions(BaseModel):
    """Data model for the generated solutions."""
    solutions: List[str] = Field(description="A list of 2-3 distinct and valid code solutions for the given problem.")

class InitialAnalysis(BaseModel):
    """Data model for the initial analysis of the candidate's code."""
    is_similar: bool = Field(description="Whether the candidate's code is substantially similar to any of the generated solutions.")
    explanation: str = Field(description="A brief explanation for the verdict, highlighting similarities or differences.")
    certainty_score: float = Field(description="A score from 0.0 to 1.0 indicating the model's confidence in its verdict.")

class FinalVerdict(BaseModel):
    """Data model for the final, expert verdict on the candidate's code."""
    cheating_probability: float = Field(description="A probability score from 0.0 to 1.0 indicating the likelihood of cheating.")
    reasoning: str = Field(description="A detailed reasoning for the final verdict, considering all evidence.")

class AnalysisPayload(BaseModel):
    """Data model for the input to the analysis graph."""
    question: str
    candidate_code: str
    session_id: str

# 3. Set up the models
LLM_PROVIDER = os.getenv("LLM_PROVIDER", "google").lower()
MODEL_MAX_RETRIES = int(os.getenv("MODEL_MAX_RETRIES", "2"))

def get_chat_model(model_name: str, temperature: float):
    """Factory function to get the appropriate chat model based on the provider."""
    model = None
    if LLM_PROVIDER == "openrouter":
        print(f"---Using OpenRouter model: {model_name}---")
        model = ChatOpenAI(
            model=model_name,
            temperature=temperature,
            api_key=os.getenv("OPENROUTER_API_KEY"),
            base_url=os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
        )
    else:  # Default to Google
        print(f"---Using Google model: {model_name}---")
        model = ChatGoogleGenerativeAI(model=model_name, temperature=temperature, google_api_key=os.getenv("GOOGLE_API_KEY"))
    return model

# Model names are sourced from environment variables with sensible defaults.
SOLUTION_MODEL_NAME = os.getenv("SOLUTION_MODEL", "google/gemini-2.5-flash-lite-preview-06-17")
ANALYSIS_MODEL_NAME = os.getenv("ANALYSIS_MODEL", "google/gemini-2.5-flash")
EXPERT_MODEL_NAME = os.getenv("EXPERT_MODEL", "google/gemini-2.5-pro")

solution_model = get_chat_model(SOLUTION_MODEL_NAME, temperature=0.3)
analysis_model = get_chat_model(ANALYSIS_MODEL_NAME, temperature=0)
expert_model = get_chat_model(EXPERT_MODEL_NAME, temperature=0)

# 4. Implement Agent Nodes
def solution_generation_node(state: GraphState):
    """
    Generates potential solutions for the given interview question.
    """
    print("---NODE: GENERATING SOLUTIONS---")
    question = state["question"]

    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a senior software engineer. Your task is to generate 2-3 distinct and valid code solutions for the given programming problem. Respond with a JSON object containing a 'solutions' key, which is a list of strings."),
        ("user", "{question}")
    ])

    parser = JsonOutputParser()
    chain = prompt | solution_model | parser
    chain_with_retry = chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    try:
        print(f"---Invoking solution generation chain with retry ({MODEL_MAX_RETRIES} attempts)---")
        result_dict = chain_with_retry.invoke({"question": question})
        parsed_output = GeneratedSolutions.parse_obj(result_dict)
        print(f"---Generated {len(parsed_output.solutions)} solutions---")
        return {"generated_solutions": parsed_output.solutions}
    except Exception as e:
        print(f"Error in solution generation: {e}")
        return {"error": "Failed to generate solutions."}

def initial_analysis_node(state: GraphState):
    """
    Performs an initial analysis of the candidate's code against the generated solutions.
    """
    print("---NODE: PERFORMING INITIAL ANALYSIS---")
    question = state["question"]
    candidate_code = state["candidate_code"]
    generated_solutions = state["generated_solutions"]

    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a code reviewer tasked with detecting possible plagiarism in coding interviews. Compare the candidate's code to the provided AI-generated solutions, considering both similarities and differences. Do not assume plagiarism simply because of common approaches; only mark 'is_similar' as true if the code closely matches one of the AI solutions in logic and structure. Provide a brief 'explanation' of your reasoning and a 'certainty_score' (0.0 to 1.0) reflecting confidence in your verdict. Respond only with a JSON object containing these fields."),
        ("user", "Problem Question:\n{question}\n\nAI-Generated Solutions:\n{solutions}\n\nCandidate's Code:\n{code}")
    ])

    analysis_parser = JsonOutputParser()
    analysis_chain = prompt | analysis_model | analysis_parser
    analysis_chain = analysis_chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    try:
        print(f"---Invoking initial analysis chain with retry ({MODEL_MAX_RETRIES} attempts)---")
        result_dict = analysis_chain.invoke({
            "question": question,
            "solutions": "\n---\n".join(generated_solutions),
            "code": candidate_code
        })
        parsed_output = InitialAnalysis.parse_obj(result_dict)
        print(f"---Initial analysis complete. Certainty: {parsed_output.certainty_score}---")
        is_uncertain = parsed_output.certainty_score < 0.9
        return {
            "analysis_details": parsed_output.dict(),
            "is_uncertain": is_uncertain,
        }
    except Exception as e:
        print(f"Error in initial analysis: {e}")
        return {"error": "Failed to perform initial analysis."}

def expert_analysis_node(state: GraphState):
    """
    Performs a more detailed analysis using a powerful model when the initial verdict is uncertain.
    """
    print("---NODE: PERFORMING EXPERT ANALYSIS---")
    question = state["question"]
    candidate_code = state["candidate_code"]
    generated_solutions = state["generated_solutions"]
    initial_analysis_text = state["analysis_details"]["explanation"]

    prompt = ChatPromptTemplate.from_messages([
        ("system", "You are a principal engineer and an expert in academic integrity tasked with providing a final verdict on whether a candidate's code was likely plagiarized or not. The initial analysis was inconclusive, so you need to deliver a clear and decisive conclusion. Write the 'reasoning' in a neutral, third-person tone as a formal report, without referring to yourself or your role. Begin the reasoning with a concise 2-3 sentence summary of your verdict, then expand with a detailed explanation including key supporting points. Respond with a JSON object containing 'cheating_probability' (float from 0.0 to 1.0) and 'reasoning' (the explained verdict)."),
        ("user", "Problem Question:\n{question}\n\nAI-Generated Solutions:\n{solutions}\n\nCandidate's Code:\n{code}\n\nInitial (Uncertain) Analysis:\n{initial_analysis}")
    ])

    expert_parser = JsonOutputParser()
    expert_chain = prompt | expert_model | expert_parser
    expert_chain = expert_chain.with_retry(stop_after_attempt=MODEL_MAX_RETRIES)

    try:
        print(f"---Invoking expert analysis chain with retry ({MODEL_MAX_RETRIES} attempts)---")
        result_dict = expert_chain.invoke({
            "question": question,
            "solutions": "\n---\n".join(generated_solutions),
            "code": candidate_code,
            "initial_analysis": initial_analysis_text
        })
        parsed_output = FinalVerdict.parse_obj(result_dict)
        print(f"---Expert analysis complete. Probability: {parsed_output.cheating_probability}---")
        return {"final_verdict": parsed_output.dict()}
    except Exception as e:
        print(f"Error in expert analysis: {e}")
        return {"error": "Failed to perform expert analysis."}

def should_continue_to_expert(state: GraphState):
    """
    Determines whether to proceed to the expert analysis node or end the workflow.
    """
    print("---CONDITIONAL EDGE: SHOULD CONTINUE?---")
    if state.get("error"):
        print("---Error detected, ending graph---")
        return "end"
    is_uncertain = state.get("is_uncertain", False)
    if is_uncertain:
        print("---Verdict is uncertain. Escalating to expert.---")
        return "expert_analysis"
    else:
        print("---Verdict is certain. Finishing.---")
        return "end"

# 6. Assemble the graph
from langgraph.graph import StateGraph, END

workflow = StateGraph(GraphState)
workflow.add_node("solution_generation", solution_generation_node)
workflow.add_node("initial_analysis", initial_analysis_node)
workflow.add_node("expert_analysis", expert_analysis_node)
workflow.set_entry_point("solution_generation")
workflow.add_edge("solution_generation", "initial_analysis")
workflow.add_conditional_edges(
    "initial_analysis",
    should_continue_to_expert,
    {
        "expert_analysis": "expert_analysis",
        "end": END
    }
)
workflow.add_edge("expert_analysis", END)
app = workflow.compile()

async def stream_ai_analysis(db: AsyncSession, payload: AnalysisPayload, sio_instance):
    """
    Runs the full AI analysis, streaming progress updates via WebSockets and logging
    the final results to MLflow.
    """
    room = f"session_{payload.session_id}"
    node_to_message = {
        "solution_generation": "Generating potential solutions...",
        "initial_analysis": "Performing initial analysis...",
        "expert_analysis": "Initial analysis uncertain. Invoking expert model...",
    }

    # --- Step 1: Execute the graph within an MLflow run context ---
    experiment_name = f"Session_{payload.session_id}"
    mlflow.set_experiment(experiment_name)
    with mlflow.start_run() as run:
        print(f"---[MLflow] Started run {run.info.run_id} for session {payload.session_id}---")
        run_id = run.info.run_id
        inputs = {"question": payload.question, "candidate_code": payload.candidate_code}
        config = {"configurable": {"thread_id": run_id}}
        final_state = None

        try:
            print("---Streaming AI analysis graph...---")
            async for event in app.astream_events(inputs, config, version="v1"):
                kind = event["event"]
                if kind == "on_chain_start":
                    node_name = event["name"]
                    if node_name in node_to_message:
                        message = node_to_message[node_name]
                        print(f"---[Progress] {message}---")
                        await sio_instance.emit("analysis_progress", {"message": message}, room=room)
                elif kind == "on_chain_end":
                    if event["name"] == "LangGraph":  # The root graph is named "LangGraph" by default
                        final_state = event["data"]["output"]
            print("---Graph streaming complete.---")
        except Exception as e:
            print(f"---[ERROR] Graph execution failed: {e}---")
            final_state = {"error": str(e)}
            mlflow.log_param("error", str(e))

        # --- Step 2: Determine final verdict ---
        final_verdict = None
        if isinstance(final_state, dict):
            expert_analysis_result = final_state.get("expert_analysis")
            if expert_analysis_result and expert_analysis_result.get("final_verdict"):
                # Expert analysis was performed – use its verdict
                expert_verdict = expert_analysis_result["final_verdict"]
                probability = expert_verdict.get("cheating_probability", 0.0)
                if probability > 0.75:
                    verdict_str = "High-Confidence Match"
                elif probability > 0.4:
                    verdict_str = "Potential Match"
                else:
                    verdict_str = "No Cheating Detected"
                final_verdict = {
                    "verdict": verdict_str,
                    "explanation": expert_verdict.get("reasoning", "N/A"),
                    "certainty_score": 1.0,  # Expert model is considered fully certain in its conclusion
                    "cheating_probability": probability,
                }
            elif final_state.get("initial_analysis"):
                # No expert step, so use the initial analysis outcome
                initial_analysis_result = final_state.get("initial_analysis", {})
                analysis_details = initial_analysis_result.get("analysis_details")
                if analysis_details:
                    is_similar = analysis_details.get("is_similar", False)
                    # Use the initial model's certainty to set cheating probability instead of a binary value
                    probability = analysis_details.get("certainty_score", 0.0) if is_similar else 1.0 - analysis_details.get("certainty_score", 0.0)
                    if probability > 0.75:
                        verdict_str = "High-Confidence Match"
                    elif probability > 0.4:
                        verdict_str = "Potential Match"
                    else:
                        verdict_str = "No Cheating Detected"
                    final_verdict = {
                        "verdict": verdict_str,
                        "explanation": analysis_details.get("explanation", "N/A"),
                        "certainty_score": analysis_details.get("certainty_score", 0.0),
                        "cheating_probability": probability,
                    }

        # --- Step 3: Log params, metrics, and artifacts ---
        print("---[MLflow] Logging params, metrics, and artifacts...---")
        mlflow.log_param("question", payload.question)
        mlflow.log_param("llm_provider", LLM_PROVIDER)
        if final_verdict:
            mlflow.log_metric("cheating_probability", final_verdict.get("cheating_probability", 0.0))
            with tempfile.NamedTemporaryFile("w", delete=False, suffix=".txt", encoding="utf-8") as tmp:
                tmp.write(payload.candidate_code)
                mlflow.log_artifact(tmp.name, "candidate_code.txt")
            os.unlink(tmp.name)
            with tempfile.NamedTemporaryFile("w", delete=False, suffix=".txt", encoding="utf-8") as tmp:
                tmp.write(final_verdict.get("explanation", "N/A"))
                mlflow.log_artifact(tmp.name, "explanation.txt")
            os.unlink(tmp.name)
        print(f"---[MLflow] Run finished successfully.---")

        # --- Step 4: Broadcast final results to the client ---
        print(f"---Broadcasting analysis results to room: {room}---")
        await sio_instance.emit("analysis_complete", {"final_verdict": final_verdict}, room=room)
        print(f"---Successfully broadcast to room: {room}---")
