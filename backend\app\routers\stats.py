"""Statistics endpoints for admin dashboard."""
from datetime import datetime

from fastapi import APIRouter, Depends
from pydantic import BaseModel
from sqlalchemy import func, select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import get_logger
from app.database import get_db
from ..models.schemas import InterviewSession

router = APIRouter(prefix="/stats", tags=["stats"])

# Initialize logging
logger = get_logger("stats")


class StatsResponse(BaseModel):
    total_sessions: int
    active_sessions: int
    total_candidates: int
    success_rate: int  # placeholder
@router.get("/", response_model=StatsResponse)
async def get_stats(db: AsyncSession = Depends(get_db)):
    logger.info("Retrieving platform statistics")
    now = datetime.utcnow()

    total_sessions_result = await db.execute(select(func.count()).select_from(InterviewSession))
    total_sessions = total_sessions_result.scalar_one()

    active_sessions_result = await db.execute(
        select(func.count()).select_from(InterviewSession).where(InterviewSession.is_active == True, InterviewSession.expires_at > now)
    )
    active_sessions = active_sessions_result.scalar_one()

    candidates_result = await db.execute(select(func.count(func.distinct(InterviewSession.candidate_name))))
    total_candidates = candidates_result.scalar_one()

    logger.info("Platform statistics retrieved",
               total_sessions=total_sessions,
               active_sessions=active_sessions,
               total_candidates=total_candidates)

    return StatsResponse(
        total_sessions=total_sessions,
        active_sessions=active_sessions,
        total_candidates=total_candidates,
        success_rate=0,
    )
