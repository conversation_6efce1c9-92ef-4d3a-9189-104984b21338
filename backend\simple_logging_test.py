#!/usr/bin/env python3
"""
Simple test script to verify logging implementation works correctly.
"""

import sys
from pathlib import Path

# Add the app directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

def test_logging():
    """Test basic logging functionality."""
    print("=== Testing Basic Logging ===")
    
    try:
        # Import logging components
        from app.core.logging import configure_logging, get_logger, LogContext, PerformanceLogger
        print("✓ Logging modules imported successfully")
        
        # Configure logging
        configure_logging()
        print("✓ Logging configured successfully")
        
        # Test basic logging
        logger = get_logger("test.simple")
        logger.info("This is a test info message", component="test")
        logger.warning("This is a test warning", component="test")
        logger.error("This is a test error", component="test")
        print("✓ Basic logging works")
        
        # Test LogContext
        session_ctx = LogContext.session("test_session", "candidate")
        logger.info("Session test", **session_ctx)
        
        request_ctx = LogContext.request("GET", "/api/test")
        logger.info("Request test", **request_ctx)
        print("✓ LogContext works")
        
        # Test PerformanceLogger
        import time
        with PerformanceLogger(logger, "test_operation", component="test"):
            time.sleep(0.1)
        print("✓ PerformanceLogger works")
        
        print("\n🎉 All basic logging tests passed!")
        print("The logging system is working correctly.")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_logging()
    sys.exit(0 if success else 1)
