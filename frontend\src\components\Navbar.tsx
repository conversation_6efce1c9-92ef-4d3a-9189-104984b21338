"use client";
import Link from "next/link";
import { ThemeToggle } from "@/components/ThemeToggle";
import { LogOut } from "lucide-react";
import { removeToken } from "@/lib/api";
import { useRouter } from "next/navigation";

export function Navbar() {
  const router = useRouter();
  return (
    <nav className="sticky top-0 z-30 w-full border-b bg-background/80 backdrop-blur">
      <div className="container mx-auto flex h-14 items-center justify-between px-6 md:px-8">
        <Link href="/" className="flex items-center font-semibold text-lg">
          <span className="mr-1 text-primary">&lt;/&gt;</span> Code Guardians
        </Link>
        <div className="flex items-center gap-2">
          <ThemeToggle />
          <button
            onClick={() => {
              removeToken();
              router.replace("/admin/login");
            }}
            className="inline-flex items-center gap-1 px-2 py-2 rounded-md hover:bg-accent text-foreground"
            title="Logout"
          >
            <LogOut className="h-4 w-4 mr-1" /> Logout
          </button>
        </div>
      </div>
    </nav>
  );
}
