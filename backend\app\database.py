"""Centralized database session management."""
from typing import Async<PERSON>enerator
import time

from sqlalchemy.ext.asyncio import AsyncSession

from app.core.db import async_session
from app.core.logging import get_logger

# Initialize database session logger
session_logger = get_logger("database.session")


async def get_db() -> AsyncGenerator[AsyncSession, None]:
    """Dependency to get a DB session."""
    start_time = time.time()
    session_logger.debug("Creating database session")

    try:
        async with async_session() as session:
            session_logger.debug("Database session created successfully")
            yield session
    except Exception as e:
        session_logger.error("Database session error", error=str(e))
        raise
    finally:
        duration = time.time() - start_time
        session_logger.debug("Database session closed", duration_ms=round(duration * 1000, 2))
