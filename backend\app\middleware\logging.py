"""FastAPI logging middleware for request/response logging."""
import time
import uuid
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

from app.core.logging import get_logger, LogContext, PerformanceLogger


class LoggingMiddleware(BaseHTTPMiddleware):
    """Middleware to log HTTP requests and responses."""
    
    def __init__(self, app, logger_name: str = "http"):
        super().__init__(app)
        self.logger = get_logger(logger_name)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request and log details."""
        # Generate unique request ID
        request_id = str(uuid.uuid4())
        
        # Extract request details
        method = request.method
        path = request.url.path
        query_params = str(request.query_params) if request.query_params else None
        client_ip = request.client.host if request.client else "unknown"
        user_agent = request.headers.get("user-agent", "unknown")
        
        # Create request context
        context = LogContext.request(method, path, request_id)
        context.update({
            "client_ip": client_ip,
            "user_agent": user_agent,
        })
        
        if query_params:
            context["query_params"] = query_params
        
        # Log incoming request
        self.logger.info(
            "Incoming request",
            **context
        )
        
        # Process request with performance tracking
        start_time = time.time()
        try:
            response = await call_next(request)
            duration = time.time() - start_time
            
            # Log successful response
            self.logger.info(
                "Request completed",
                status_code=response.status_code,
                duration_seconds=round(duration, 3),
                **context
            )
            
            return response
            
        except Exception as exc:
            duration = time.time() - start_time
            
            # Log error response
            self.logger.error(
                "Request failed",
                error=str(exc),
                error_type=type(exc).__name__,
                duration_seconds=round(duration, 3),
                **context
            )
            raise


class DatabaseLoggingMiddleware:
    """Middleware for database operation logging."""
    
    def __init__(self, logger_name: str = "database"):
        self.logger = get_logger(logger_name)
    
    def log_query(self, operation: str, table: str = None, **kwargs):
        """Log database query."""
        context = LogContext.database(operation, table)
        context.update(kwargs)
        
        self.logger.debug(
            f"Database {operation}",
            **context
        )
    
    def log_transaction(self, operation: str, **kwargs):
        """Log database transaction."""
        context = LogContext.database(operation)
        context.update(kwargs)
        
        self.logger.info(
            f"Database transaction {operation}",
            **context
        )


class WebSocketLoggingMixin:
    """Mixin for WebSocket event logging."""
    
    def __init__(self):
        self.logger = get_logger("websocket")
    
    def log_connection(self, sid: str, event: str = "connect", **kwargs):
        """Log WebSocket connection events."""
        context = LogContext.websocket(event)
        context.update({"socket_id": sid, **kwargs})
        
        self.logger.info(
            f"WebSocket {event}",
            **context
        )
    
    def log_event(self, event: str, session_id: str = None, **kwargs):
        """Log WebSocket events."""
        context = LogContext.websocket(event, session_id)
        context.update(kwargs)
        
        self.logger.debug(
            f"WebSocket event: {event}",
            **context
        )
    
    def log_error(self, event: str, error: str, session_id: str = None, **kwargs):
        """Log WebSocket errors."""
        context = LogContext.websocket(event, session_id)
        context.update({"error": error, **kwargs})
        
        self.logger.error(
            f"WebSocket error in {event}",
            **context
        )


# Global database logging instance
db_logger = DatabaseLoggingMiddleware()
