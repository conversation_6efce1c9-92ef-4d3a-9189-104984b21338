"""Auth endpoints."""
from fastapi import APIRout<PERSON>, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import get_logger
from app.database import get_db
from app.core.db import async_session
from ..core.security import verify_password, create_access_token, get_password_hash
from ..models.admin import Admin

router = APIRouter(prefix="/auth", tags=["auth"])

# Initialize logging
logger = get_logger("auth")


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class LoginPayload(BaseModel):
    username: str
    password: str


@router.post("/login", response_model=Token)
async def login(payload: LoginPayload, db: AsyncSession = Depends(get_db)):
    logger.info("Login attempt", username=payload.username)

    result = await db.execute(select(Admin).where(Admin.username == payload.username))
    admin: Admin | None = result.scalar_one_or_none()
    if not admin or not verify_password(payload.password, admin.hashed_password):
        logger.warning("Login failed - invalid credentials", username=payload.username)
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    token = create_access_token({"sub": str(admin.id)})
    logger.info("Login successful", username=payload.username, admin_id=admin.id)
    return Token(access_token=token)


# Helper to create default admin on startup if none exists
async def ensure_default_admin():
    logger.info("Checking for default admin user")
    async with async_session() as session:
        result = await session.execute(select(Admin))
        if result.scalar_one_or_none() is None:
            logger.info("Creating default admin user")
            admin = Admin(username="admin", hashed_password=get_password_hash("admin"))
            session.add(admin)
            await session.commit()
            logger.info("Default admin user created successfully")
        else:
            logger.debug("Admin user already exists")
