"""Auth endpoints."""
from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPException, status
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from app.core.db import async_session
from ..core.security import verify_password, create_access_token, get_password_hash
from ..models.admin import Admin

router = APIRouter(prefix="/auth", tags=["auth"])


class Token(BaseModel):
    access_token: str
    token_type: str = "bearer"


class LoginPayload(BaseModel):
    username: str
    password: str


@router.post("/login", response_model=Token)
async def login(payload: LoginPayload, db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(Admin).where(Admin.username == payload.username))
    admin: Admin | None = result.scalar_one_or_none()
    if not admin or not verify_password(payload.password, admin.hashed_password):
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")

    token = create_access_token({"sub": str(admin.id)})
    return Token(access_token=token)


# Helper to create default admin on startup if none exists
async def ensure_default_admin():
    async with async_session() as session:
        result = await session.execute(select(Admin))
        if result.scalar_one_or_none() is None:
            admin = Admin(username="admin", hashed_password=get_password_hash("admin"))
            session.add(admin)
            await session.commit()
