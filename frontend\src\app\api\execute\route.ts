import { NextRequest, NextResponse } from "next/server";

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:8000";

export async function POST(req: NextRequest) {
  try {
    const { code } = await req.json();
    if (typeof code !== "string") {
      return NextResponse.json({ error: "Invalid payload" }, { status: 400 });
    }

    const res = await fetch(`${API_URL}/execute/python`, {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ code }),
      // node-fetch in Next automatically follows redirects
    });

    if (!res.ok) {
      const text = await res.text();
      return NextResponse.json({ error: text || "Execution error" }, { status: res.status });
    }

    const data = await res.json();
    // Assuming backend returns { output: string }
    return NextResponse.json({ output: data.output ?? JSON.stringify(data) });
  } catch (err: any) {
    return NextResponse.json({ error: err.message ?? "Unknown error" }, { status: 500 });
  }
}
