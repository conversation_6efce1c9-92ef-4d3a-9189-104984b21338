"""Endpoints to execute code via go-judge (Python & Java)."""
from datetime import datetime

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.core.logging import get_logger, LogContext, PerformanceLogger
from app.database import async_session, get_db
from app.models.schemas import ExecutionEvent, InterviewArtifact
from app.services.ai_analysis_service import AnalysisPayload, stream_ai_analysis
from app.services.go_judge import GoJudgeService
from ..sockets import sio

router = APIRouter(prefix="/execute", tags=["execute"])

# Initialize logging
logger = get_logger("code_execution")


class ExecutePayload(BaseModel):
    session_id: str
    user_type: str  # candidate/interviewer
    code: str
    execution_start_ms: int
    artifact_id: int | None = None


class ExecuteResult(BaseModel):
    output: str | None
    error: str | None
    memory: int | None
    time: float | None
    status: str | None = None
    exit_status: int | None = None


async def trigger_ai_analysis(session_id: str, code: str, artifact_id: int | None, sio_instance):
    """
    Asynchronously triggers AI analysis if an artifact_id (question) is present.
    """
    logger.info("AI analysis trigger function called",
               session_id=session_id,
               artifact_id=artifact_id,
               code_length=len(code))

    if not artifact_id:
        logger.warning("No artifact_id provided, skipping AI analysis",
                      session_id=session_id,
                      artifact_id=artifact_id,
                      reason="artifact_id is None or 0")
        return

    # Create a new DB session for the background task
    async with async_session() as session:
        try:
            result = await session.execute(
                select(InterviewArtifact.content).where(
                    InterviewArtifact.id == artifact_id
                )
            )
            question = result.scalar_one_or_none()
        except Exception as e:
            logger.error("Database error while fetching question for analysis",
                        error=str(e),
                        session_id=session_id,
                        artifact_id=artifact_id)
            return

        if not question:
            logger.warning("Could not find artifact (question) for analysis",
                          artifact_id=artifact_id,
                          session_id=session_id)
            return

        analysis_payload = AnalysisPayload(question=question, candidate_code=code, session_id=session_id)
        logger.info("Triggering AI analysis",
                   session_id=session_id,
                   artifact_id=artifact_id)
        try:
            await stream_ai_analysis(db=session, payload=analysis_payload, sio_instance=sio_instance)
        except Exception as e:
            logger.error("Error during AI analysis background task",
                        error=str(e),
                        session_id=session_id)


@router.post("/python", response_model=ExecuteResult)
async def execute_python(
    payload: ExecutePayload, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)
):
    logger.info("Python code execution requested",
               session_id=payload.session_id,
               user_type=payload.user_type,
               artifact_id=payload.artifact_id,
               code_length=len(payload.code))

    # Debug log to help identify AI analysis trigger issues
    logger.debug("Execution payload details",
                session_id=payload.session_id,
                user_type=payload.user_type,
                user_type_check=payload.user_type == "interviewer",
                artifact_id=payload.artifact_id,
                has_artifact=payload.artifact_id is not None)

    with PerformanceLogger(logger, "python_execution"):
        async with GoJudgeService() as judge:
            try:
                result = await judge.execute_python(payload.code)
                logger.info("Python execution completed",
                           session_id=payload.session_id,
                           has_output=bool(result.get("stdout")),
                           has_error=bool(result.get("stderr")),
                           memory_usage=result.get("memory"),
                           execution_time_ns=result.get("time"))
            except Exception as exc:
                logger.error("Python execution failed",
                           session_id=payload.session_id,
                           error=str(exc))
                raise HTTPException(status_code=500, detail="Execution error") from exc

    # Store event
    event = ExecutionEvent(
        session_id=payload.session_id,
        user_type=payload.user_type,
        code_snapshot=payload.code,
        execution_start_ms=payload.execution_start_ms,
        execution_end_ms=int(datetime.utcnow().timestamp() * 1000),
        output=result.get("stdout"),
        error_message=result.get("stderr"),
        memory_usage=result.get("memory"),
        cpu_time_ms=int(result.get("time") / 1_000_000) if result.get("time") else None,
        artifact_id=payload.artifact_id,
    )
    db.add(event)
    await db.commit()
    logger.debug("Execution event stored in database", session_id=payload.session_id)

    # Trigger analysis in the background only for the interviewer
    logger.debug("Checking AI analysis trigger conditions",
                session_id=payload.session_id,
                user_type=payload.user_type,
                is_interviewer=payload.user_type == "interviewer",
                artifact_id=payload.artifact_id,
                has_artifact=payload.artifact_id is not None)

    # TEMPORARY: Always trigger for testing - REMOVE THIS LATER
    if payload.user_type == "interviewer" or True:
        background_tasks.add_task(
            trigger_ai_analysis, payload.session_id, payload.code, payload.artifact_id, sio
        )
        logger.info("AI analysis task scheduled",
                   session_id=payload.session_id,
                   artifact_id=payload.artifact_id)
    else:
        logger.debug("AI analysis not triggered - user is not interviewer",
                    session_id=payload.session_id,
                    user_type=payload.user_type)

    return ExecuteResult(
        output=result.get("stdout"),
        error=result.get("stderr"),
        memory=result.get("memory"),
        time=float(result.get("time") / 1_000_000_000) if result.get("time") else 0.0,
        status=result.get("status"),
        exit_status=result.get("exit_status"),
    )


@router.post("/java", response_model=ExecuteResult)
async def execute_java(
    payload: ExecutePayload, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)
):
    logger.info("Java code execution requested",
               session_id=payload.session_id,
               user_type=payload.user_type,
               artifact_id=payload.artifact_id)

    with PerformanceLogger(logger, "java_execution"):
        async with GoJudgeService() as judge:
            try:
                result = await judge.execute_java(payload.code)
                logger.info("Java execution completed",
                           session_id=payload.session_id,
                           has_output=bool(result.get("stdout")),
                           has_error=bool(result.get("stderr")),
                           memory_usage=result.get("memory"),
                           execution_time_ns=result.get("time"))
            except Exception as exc:
                logger.error("Java execution failed",
                           session_id=payload.session_id,
                           error=str(exc))
                raise HTTPException(status_code=500, detail="Execution error") from exc

    event = ExecutionEvent(
        session_id=payload.session_id,
        user_type=payload.user_type,
        code_snapshot=payload.code,
        execution_start_ms=payload.execution_start_ms,
        execution_end_ms=int(datetime.utcnow().timestamp() * 1000),
        output=result.get("stdout"),
        error_message=result.get("stderr"),
        memory_usage=result.get("memory"),
        cpu_time_ms=int(result.get("time") / 1_000_000) if result.get("time") else None,
        artifact_id=payload.artifact_id,
    )
    db.add(event)
    await db.commit()
    logger.debug("Execution event stored in database", session_id=payload.session_id)

    # Trigger analysis in the background only for the interviewer
    logger.debug("Checking AI analysis trigger conditions",
                session_id=payload.session_id,
                user_type=payload.user_type,
                is_interviewer=payload.user_type == "interviewer",
                artifact_id=payload.artifact_id,
                has_artifact=payload.artifact_id is not None)

    if payload.user_type == "interviewer":
        background_tasks.add_task(
            trigger_ai_analysis, payload.session_id, payload.code, payload.artifact_id, sio
        )
        logger.info("AI analysis task scheduled",
                   session_id=payload.session_id,
                   artifact_id=payload.artifact_id)
    else:
        logger.debug("AI analysis not triggered - user is not interviewer",
                    session_id=payload.session_id,
                    user_type=payload.user_type)

    return ExecuteResult(
        output=result.get("stdout"),
        error=result.get("stderr"),
        memory=result.get("memory"),
        time=float(result.get("time") / 1_000_000_000) if result.get("time") else 0.0,
        status=result.get("status"),
        exit_status=result.get("exit_status"),
    )
