"""Endpoints to execute code via go-judge (Python & Java)."""
from datetime import datetime

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from pydantic import BaseModel
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import async_session, get_db
from app.models.schemas import ExecutionEvent, InterviewArtifact
from app.services.ai_analysis_service import AnalysisPayload, stream_ai_analysis
from app.services.go_judge import GoJudgeService
from ..sockets import sio

router = APIRouter(prefix="/execute", tags=["execute"])


class ExecutePayload(BaseModel):
    session_id: str
    user_type: str  # candidate/interviewer
    code: str
    execution_start_ms: int
    artifact_id: int | None = None


class ExecuteResult(BaseModel):
    output: str | None
    error: str | None
    memory: int | None
    time: float | None
    status: str | None = None
    exit_status: int | None = None


async def trigger_ai_analysis(session_id: str, code: str, artifact_id: int | None, sio_instance):
    """
    Asynchronously triggers AI analysis if an artifact_id (question) is present.
    """
    if not artifact_id:
        print("No artifact_id provided, skipping AI analysis.")
        return

    # Create a new DB session for the background task
    async with async_session() as session:
        try:
            result = await session.execute(
                select(InterviewArtifact.content).where(
                    InterviewArtifact.id == artifact_id
                )
            )
            question = result.scalar_one_or_none()
        except Exception as e:
            print(f"Database error while fetching question for analysis: {e}")
            return

        if not question:
            print(f"Could not find artifact (question) with id {artifact_id}, skipping analysis.")
            return

        analysis_payload = AnalysisPayload(question=question, candidate_code=code, session_id=session_id)
        print(f"---Triggering AI analysis for session {session_id}---")
        try:
            await stream_ai_analysis(db=session, payload=analysis_payload, sio_instance=sio_instance)
        except Exception as e:
            print(f"Error during AI analysis background task: {e}")


@router.post("/python", response_model=ExecuteResult)
async def execute_python(
    payload: ExecutePayload, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)
):
    async with GoJudgeService() as judge:
        try:
            result = await judge.execute_python(payload.code)
        except Exception as exc:
            raise HTTPException(status_code=500, detail="Execution error") from exc

    # Store event
    event = ExecutionEvent(
        session_id=payload.session_id,
        user_type=payload.user_type,
        code_snapshot=payload.code,
        execution_start_ms=payload.execution_start_ms,
        execution_end_ms=int(datetime.utcnow().timestamp() * 1000),
        output=result.get("stdout"),
        error_message=result.get("stderr"),
        memory_usage=result.get("memory"),
        cpu_time_ms=int(result.get("time") / 1_000_000) if result.get("time") else None,
        artifact_id=payload.artifact_id,
    )
    db.add(event)
    await db.commit()

    # Trigger analysis in the background only for the interviewer
    if payload.user_type == "interviewer":
        background_tasks.add_task(
            trigger_ai_analysis, payload.session_id, payload.code, payload.artifact_id, sio
        )

    return ExecuteResult(
        output=result.get("stdout"),
        error=result.get("stderr"),
        memory=result.get("memory"),
        time=float(result.get("time") / 1_000_000_000) if result.get("time") else 0.0,
        status=result.get("status"),
        exit_status=result.get("exit_status"),
    )


@router.post("/java", response_model=ExecuteResult)
async def execute_java(
    payload: ExecutePayload, background_tasks: BackgroundTasks, db: AsyncSession = Depends(get_db)
):
    async with GoJudgeService() as judge:
        try:
            result = await judge.execute_java(payload.code)
        except Exception as exc:
            raise HTTPException(status_code=500, detail="Execution error") from exc

    event = ExecutionEvent(
        session_id=payload.session_id,
        user_type=payload.user_type,
        code_snapshot=payload.code,
        execution_start_ms=payload.execution_start_ms,
        execution_end_ms=int(datetime.utcnow().timestamp() * 1000),
        output=result.get("stdout"),
        error_message=result.get("stderr"),
        memory_usage=result.get("memory"),
        cpu_time_ms=int(result.get("time") / 1_000_000) if result.get("time") else None,
        artifact_id=payload.artifact_id,
    )
    db.add(event)
    await db.commit()

    # Trigger analysis in the background only for the interviewer
    if payload.user_type == "interviewer":
        background_tasks.add_task(
            trigger_ai_analysis, payload.session_id, payload.code, payload.artifact_id, sio
        )

    return ExecuteResult(
        output=result.get("stdout"),
        error=result.get("stderr"),
        memory=result.get("memory"),
        time=float(result.get("time") / 1_000_000_000) if result.get("time") else 0.0,
        status=result.get("status"),
        exit_status=result.get("exit_status"),
    )
