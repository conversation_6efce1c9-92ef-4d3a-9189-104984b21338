services:
  # ===== FRONTEND =====
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
      - NEXT_PUBLIC_WS_URL=ws://localhost:8000
    depends_on:
      - backend

  # ===== BACKEND =====
  backend:
    build: ./backend
    volumes:
      - ./backend:/app
      - ./mlruns:/mlruns
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - DATABASE_URL=postgresql+asyncpg://user:password@postgres:5432/interview_platform
      - GO_JUDGE_URL=http://go-judge:5050
      - REDIS_URL=redis://redis:6379/0
      - MLFLOW_TRACKING_URI=http://mlflow:5000
    env_file: ./.env
    depends_on:
      - postgres
      - redis
      - go-judge
      - mlflow

  # ===== DATABASE =====
  postgres:
    image: postgres:17
    ports:
      - "5432:5432"
    environment:
      - POSTGRES_DB=interview_platform
      - POSTGRES_USER=user
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro

  # ===== REDIS =====
  redis:
    image: redis:8-alpine
    ports:
      - "6379:6379"

  # ===== MLFLOW =====
  mlflow:
    image: ghcr.io/mlflow/mlflow:v3.1.1
    container_name: mlflow-server
    ports:
      - "5001:5000"
    command: >
      mlflow server
      --host 0.0.0.0
      --backend-store-uri sqlite:///mlruns/mlruns.db
      --default-artifact-root /mlruns
    volumes:
      - ./mlruns:/mlruns
    environment:
      - MLFLOW_SERVE_ARTIFACTS=true
    restart: unless-stopped

  # ===== GO-JUDGE =====
  go-judge:
    build:
      context: ./docker/go-judge
    image: code-guardians-go-judge:latest
    platform: linux/amd64
    privileged: true
    shm_size: 256m
    ports:
      - "5050:5050"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - redis

volumes:
  postgres_data:
  mlflow_artifacts:
