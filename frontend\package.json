{"name": "code-guardians-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3000", "build": "next build", "start": "next start"}, "dependencies": {"@monaco-editor/react": "4.7.0", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-toast": "^1.2.14", "autoprefixer": "^10.4.17", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.263.1", "next": "^15.3.3", "next-themes": "^0.2.0", "postcss": "^8.4.35", "react": "18.2.0", "react-dom": "18.2.0", "react-markdown": "^10.1.0", "socket.io-client": "4.8.1", "tailwind-merge": "^3.3.1", "tailwindcss": "3.4.4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "latest", "@types/react": "18.2.37", "@types/react-dom": "18.2.15", "typescript": "5.8.3"}}