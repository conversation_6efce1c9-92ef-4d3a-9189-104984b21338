"use client";
import { useEffect, useState, ElementType, useCallback } from "react";
import { useRouter } from "next/navigation";
import { createSession, getSessions, getStats, updateSessionStatus, deleteSession } from "@/lib/api"; // Added updateSessionStatus, deleteSession
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge"; // Added Badge
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Added AlertDialog components
import { useToast } from "@/hooks/use-toast"; // Added for toast notifications
import { CalendarCheck, Activity, Users, Plus, MoreHorizontal, Search, TrendingUp, ArrowRight, Copy, Trash2, Power, X } from "lucide-react"; // Added icons for dropdown, X for dismiss
import PageTransition from "@/components/animations/PageTransition";
import { motion, AnimatePresence } from 'framer-motion';

// Interface for a single session, matching the backend response
interface Session {
  session_id: string;
  candidate_url: string;
  interviewer_url: string;
  expires_at: string; // Assuming ISO string from backend
  candidate_name: string;
  interviewer_name: string;
  job_title: string;
  created_at: string; // Assuming ISO string from backend
  is_active: boolean;
  language?: string;
}

interface Links {
  candidate_url: string;
  interviewer_url: string;
}

function formatDuration(createdAtIso: string, expiresAtIso: string): string {
  try {
    const createdAt = new Date(createdAtIso);
    const expiresAt = new Date(expiresAtIso);
    const diffMs = expiresAt.getTime() - createdAt.getTime();

    if (isNaN(diffMs) || diffMs < 0) {
      // This can happen if dates are invalid or expiresAt is before createdAt
      return "N/A";
    }

    const totalMinutes = Math.floor(diffMs / (1000 * 60));
    
    if (totalMinutes < 1 && totalMinutes >= 0) {
      // For durations less than a minute but not negative (e.g. a few seconds)
      return "<1 min"; 
    }
    if (totalMinutes < 0) { // Should be caught by diffMs < 0, but as a safeguard
        return "N/A";
    }
    
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    if (hours > 0) {
      return `${hours} hr ${minutes > 0 ? `${minutes} min` : ''}`.trim();
    } else {
      return `${minutes} min`;
    }
  } catch (e) {
    console.error("Error formatting duration:", e);
    return "N/A";
  }
}

const MotionCard = motion(Card);
const MotionButton = motion(Button);
const MotionTableRow = motion(TableRow);
const MotionDropdownMenuItem = motion(DropdownMenuItem);

export default function SessionsPage() {
  const router = useRouter();
  const { toast } = useToast(); // Initialize toast

  // Redirect unauthenticated visitors to login.
  useEffect(() => {
    const token = typeof window !== "undefined" ? localStorage.getItem("admin_token") : null;
    if (!token) {
      router.replace("/admin/login");
    }
  }, [router]);

  const [stats, setStats] = useState<{
    total_sessions: number;
    active_sessions: number;
    total_candidates: number;
    success_rate: number;
  } | null>(null);

  useEffect(() => {
    (async () => {
      const data = await getStats();
      setStats(data);
    })();
  }, []);

  const fetchSessions = useCallback(async () => {
    try {
      setSessionsLoading(true);
      const data = await getSessions(); // Assuming getSessions is implemented in lib/api
      setSessionsList(data);
      setSessionsError(null);
    } catch (err: any) {
      if (err.message === "Unauthorized") {
        router.push("/admin/login");
      }
      setSessionsError(err.message);
      setSessionsList([]); // Clear list on error
    } finally {
      setSessionsLoading(false);
    }
  }, [router]); // getSessions is stable from import; state setters are also stable

  // Fetch sessions list on component mount
  useEffect(() => {
    fetchSessions();
  }, [fetchSessions]);

  const [links, setLinks] = useState<Links | null>(null);
  const [showForm, setShowForm] = useState(false);
  const [candidateName, setCandidateName] = useState("");
  const [interviewerName, setInterviewerName] = useState("");
  const [jobTitle, setJobTitle] = useState("");
  const [newLanguage, setNewLanguage] = useState("python");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for sessions list
  const [sessionsList, setSessionsList] = useState<Session[]>([]);
  const [sessionsLoading, setSessionsLoading] = useState(true);
  const [sessionsError, setSessionsError] = useState<string | null>(null);

  // State for delete confirmation dialog
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [sessionToDeleteId, setSessionToDeleteId] = useState<string | null>(null);


  const handleCreate = async () => {
    setLoading(true);
    setError(null);
    try {
      const data = await createSession({
        candidate_name: candidateName,
        interviewer_name: interviewerName,
        job_title: jobTitle,
        language: newLanguage,
      });
      setLinks(data); // Temporarily show links for the newly created session
      toast({
        title: "Success!",
        description: "New session created successfully.",
      });

      await fetchSessions(); // Refresh the main sessions list to include the new session
      
      // Clear form fields
      setCandidateName("");
      setInterviewerName("");
      setJobTitle("");
      setNewLanguage("python");
      
      setShowForm(false); // Close/hide the creation form panel
    // Links will remain visible until manually dismissed by the user
    } catch (err: any) {
      if (err.message === "Unauthorized") {
        router.push("/admin/login");
      }
      setError(err.message);
      toast({
        title: "Error Creating Session",
        description: err.message || "An unexpected error occurred.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const [copied, setCopied] = useState<string | null>(null);
  const copy = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      setCopied(text);
      toast({
        title: "Copied!",
        description: "Link copied to clipboard.",
      });
      setTimeout(() => setCopied(null), 1500);
    }).catch(err => {
      console.error('Failed to copy text: ', err);
      toast({
        title: "Error Copying",
        description: "Could not copy link to clipboard.",
        variant: "destructive",
      });
    });
  };

  const openDeleteConfirmDialog = (sessionId: string) => {
    setSessionToDeleteId(sessionId);
    setShowDeleteConfirm(true);
  };

  const handleDeleteSession = async () => {
    if (!sessionToDeleteId) return;
    try {
      await deleteSession(sessionToDeleteId);
      setSessionsList((prevSessions) =>
        prevSessions.filter((session) => session.session_id !== sessionToDeleteId)
      );
      toast({
        title: "Session Deleted",
        description: "The session has been successfully deleted.",
      });
    } catch (err: any) {
      console.error("Failed to delete session:", err);
      setError(err.message || "Failed to delete session.");
      toast({
        title: "Error Deleting Session",
        description: err.message || "Could not delete the session.",
        variant: "destructive",
      });
    } finally {
      setSessionToDeleteId(null);
      setShowDeleteConfirm(false);
    }
  };

  const handleToggleStatus = async (sessionId: string, currentIsActive: boolean) => {
    try {
      const updatedSession = await updateSessionStatus(sessionId, !currentIsActive);
      setSessionsList((prevSessions) =>
        prevSessions.map((session) =>
          session.session_id === sessionId ? updatedSession : session
        )
      );
      toast({
        title: "Status Updated",
        description: `Session status changed to ${updatedSession.is_active ? 'Active' : 'Completed'}.`,
      });
    } catch (err: any) {
      console.error("Failed to toggle session status:", err);
      setError(err.message || "Failed to update session status.");
      toast({
        title: "Error Updating Status",
        description: err.message || "Could not update session status.",
        variant: "destructive",
      });
    }
  };

  return (
    <PageTransition>
      <div className="space-y-10">
        {/* Stats */}
        <div className="mx-auto grid w-full max-w-6xl gap-8 sm:grid-cols-3 justify-center">
          {(
            [
              {
                label: "Total Sessions",
                value: stats?.total_sessions ?? "–",
                icon: CalendarCheck,
              },
              {
                label: "Active Sessions",
                value: stats?.active_sessions ?? "–",
                icon: Activity,
              },
              {
                label: "Total Candidates",
                value: stats?.total_candidates ?? "–",
                icon: Users,
              },
            ] as { label: string; value: string | number; icon: ElementType }[]
          ).map(({ label, value, icon: Icon }) => (
            <MotionCard 
              key={label} 
              className="animate-fade-in" 
              whileHover={{ scale: 1.03, y: -2, boxShadow: "0px 10px 20px rgba(0,0,0,0.1)" }}
              transition={{ type: "spring", stiffness: 300, damping: 15 }}
            >
              <CardContent className="px-8 pt-10 pb-10 flex items-center gap-6">
                <Icon className="h-6 w-6 text-primary" />
              <div>
                <p className="text-sm text-neutral-500">{label}</p>
                <p className="text-2xl font-semibold text-foreground">{value}</p>
              </div>
            </CardContent>
            </MotionCard>
        ))}
      </div>

      {/* Session creator + list placeholder */}
      <Card className="w-full max-w-6xl mx-auto">
        <CardHeader className="px-8 py-10">
          <div className="flex items-center justify-between gap-6">
          <div>
            <CardTitle className="text-xl text-foreground">Interview Sessions</CardTitle>
            <p className="text-sm text-neutral-500">Manage and monitor all interview sessions</p>
          </div>
          <MotionButton 
              onClick={() => setShowForm((p) => !p)} 
              disabled={loading} 
              size="sm" 
              className="gap-1 ml-auto"
              whileTap={{ scale: 0.97 }}
              transition={{ type: "spring", stiffness: 500, damping: 15 }}
            >
            <Plus className="h-4 w-4" />
            {showForm ? "Close" : "New Session"}
            </MotionButton>
                  </div>
        </CardHeader>
        <CardContent className="px-8 py-6 space-y-6"> {/* Adjusted padding and spacing */}
          {error && <p className="text-sm text-red-500">{error}</p>} 
          {/* Search Bar - aligned with mockup */}
          <div className="relative mb-4">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              type="search"
              placeholder="Search sessions..."
              className="w-full pl-10"
              // TODO: Implement search functionality
            />
          </div>

          <AnimatePresence mode="wait">
            {showForm && (
            <motion.form
              key="session-form"
              initial={{ opacity: 0, height: 0, y: -20 }}
              animate={{ opacity: 1, height: 'auto', y: 0, transition: { duration: 0.3, ease: "easeInOut" } }}
              exit={{ opacity: 0, height: 0, y: -20, transition: { duration: 0.2, ease: "easeInOut" } }}
              onSubmit={(e) => {
                e.preventDefault();
                handleCreate();
              }}
              className="space-y-4"
            >
              <div className="grid sm:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm mb-1">Candidate Name</label>
                  <Input
                    value={candidateName}
                    onChange={(e) => setCandidateName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm mb-1">Interviewer Name</label>
                  <Input
                    value={interviewerName}
                    onChange={(e) => setInterviewerName(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm mb-1">Job Title</label>
                  <Input
                    placeholder="Job Title (e.g., Senior Software Engineer)"
                    value={jobTitle}
                    onChange={(e) => setJobTitle(e.target.value)}
                    required
                  />
                </div>
                <div>
                  <label htmlFor="language" className="block text-sm mb-1">Language</label>
                  <Select value={newLanguage} onValueChange={(value) => setNewLanguage(value as 'python' | 'java')}>
                    <SelectTrigger id="language" className="w-full">
                      <SelectValue placeholder="Select language" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="python">Python</SelectItem>
                      <SelectItem value="java">Java</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <Button type="submit" disabled={loading}>
                {loading ? "Creating..." : "Create Session"}
              </Button>
            </motion.form>
          )}
          </AnimatePresence>
          {links && (
            <Card className="my-4 border-green-200 dark:border-green-700 bg-green-50/50 dark:bg-green-900/20">
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg text-green-700 dark:text-green-400">New Session Created!</CardTitle>
                    <CardDescription className="text-green-600 dark:text-green-500">
                      Copy the links below and share them with the participants.
                    </CardDescription>
                  </div>
                  <div className="flex items-center gap-2 ml-auto">
                    <MotionButton 
                      onClick={() => setShowForm((p) => !p)} 
                      disabled={loading} 
                      size="sm" 
                      className="gap-1"
                      whileTap={{ scale: 0.97 }}
                      transition={{ type: "spring", stiffness: 500, damping: 15 }}
                    >
                      <Plus className="h-4 w-4" />
                      {showForm ? "Close" : "New Session"}
                    </MotionButton>
                    <MotionButton
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => setLinks(null)}
                      title="Dismiss links card"
                      whileTap={{ scale: 0.90 }}
                      transition={{ type: "spring", stiffness: 500, damping: 15 }}
                    >
                      <X className="h-4 w-4" />
                    </MotionButton>
                  </div>
                </div>
              </CardHeader>
              <CardContent className="space-y-3 pt-2">
                <div>
                  <label htmlFor="candidateLink" className="block text-xs font-medium text-muted-foreground mb-1">Candidate URL</label>
                  <div className="flex items-center gap-2">
                    <Input id="candidateLink" value={links.candidate_url} readOnly className="text-xs h-8" />
                    <MotionButton size="sm" variant="outline" onClick={() => copy(links.candidate_url)} className="h-8" whileTap={{ scale: 0.95 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                      {copied === links.candidate_url ? "Copied" : "Copy"}
                    </MotionButton>
                  </div>
                </div>
                <div>
                  <label htmlFor="interviewerLink" className="block text-xs font-medium text-muted-foreground mb-1">Interviewer URL</label>
                  <div className="flex items-center gap-2">
                    <Input id="interviewerLink" value={links.interviewer_url} readOnly className="text-xs h-8" />
                    <MotionButton size="sm" variant="outline" onClick={() => copy(links.interviewer_url)} className="h-8" whileTap={{ scale: 0.95 }} transition={{ type: "spring", stiffness: 400, damping: 17 }}>
                      {copied === links.interviewer_url ? "Copied" : "Copy"}
                    </MotionButton>
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Sessions List Table */}
          {sessionsLoading && <p className="text-sm text-muted-foreground">Loading sessions...</p>}
          {sessionsError && <p className="text-sm text-red-500">Error loading sessions: {sessionsError}</p>}
          {!sessionsLoading && !sessionsError && sessionsList.length === 0 && !showForm && (
            <p className="text-sm text-muted-foreground text-center py-8">
              No interview sessions found. Use the "+ New Session" button to create one.
            </p>
          )}
          {!sessionsLoading && !sessionsError && sessionsList.length > 0 && (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[300px]">Job Title / Names</TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Session Details</TableHead>
                  <TableHead>Date / Duration</TableHead>
                  <TableHead className="text-right w-[50px]">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {sessionsList.map((session) => (
                  <MotionTableRow
                    key={session.session_id}
                    whileHover={{
                      backgroundColor: "hsl(var(--muted))", 
                      y: -2,
                      boxShadow: "0px 3px 10px rgba(0,0,0,0.05)",
                    }}
                    transition={{ type: "spring", stiffness: 400, damping: 25 }}
                  >
                      <TableCell>
                        <div className="font-semibold">{session.job_title}</div>
                        <div className="text-xs text-muted-foreground flex items-center">
                          {session.interviewer_name} <ArrowRight className="mx-1 h-3 w-3" /> {session.candidate_name}
                        </div>
                      </TableCell>
                    <TableCell>
                      <Badge variant="outline">{session.language || "N/A"}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant={session.is_active ? "default" : "secondary"}>
                        {session.is_active ? "Active" : "Completed"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="text-sm">
                        {new Date(session.created_at).toLocaleDateString()} {new Date(session.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                      </div>
                      <div className="text-xs text-muted-foreground">
                        {formatDuration(session.created_at, session.expires_at)}
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="icon" className="h-8 w-8">
                            <MoreHorizontal className="h-4 w-4" />
                            <span className="sr-only">More options</span>
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>Actions</DropdownMenuLabel>
                          <DropdownMenuSeparator />
                          <MotionDropdownMenuItem onClick={() => copy(session.candidate_url)} whileTap={{ scale: 0.96, backgroundColor: "hsl(var(--muted) / 0.7)" }} transition={{ type: "spring", stiffness: 500, damping: 15 }}>
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Candidate Link
                          </MotionDropdownMenuItem>
                          <MotionDropdownMenuItem onClick={() => copy(session.interviewer_url)} whileTap={{ scale: 0.96, backgroundColor: "hsl(var(--muted) / 0.7)" }} transition={{ type: "spring", stiffness: 500, damping: 15 }}>
                            <Copy className="mr-2 h-4 w-4" />
                            Copy Interviewer Link
                          </MotionDropdownMenuItem>
                          <DropdownMenuSeparator />
                          <MotionDropdownMenuItem onClick={() => handleToggleStatus(session.session_id, session.is_active)} whileTap={{ scale: 0.96, backgroundColor: "hsl(var(--muted) / 0.7)" }} transition={{ type: "spring", stiffness: 500, damping: 15 }}>
                            <Power className="mr-2 h-4 w-4" />
                            Toggle Status
                          </MotionDropdownMenuItem>
                          <DropdownMenuSeparator />
                          <MotionDropdownMenuItem className="text-red-600 hover:!text-red-600 focus:!text-red-600 focus:!bg-red-50 dark:text-red-500 dark:hover:!text-red-500 dark:focus:!text-red-500 dark:focus:!bg-red-900/50" onClick={() => openDeleteConfirmDialog(session.session_id)} whileTap={{ scale: 0.96 }} transition={{ type: "spring", stiffness: 500, damping: 15 }}>
                            <Trash2 className="mr-2 h-4 w-4" />
                            Delete Session
                          </MotionDropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </MotionTableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>

      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={showDeleteConfirm} onOpenChange={setShowDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the session
              {sessionToDeleteId && sessionsList.find(s => s.session_id === sessionToDeleteId) &&
                ` for ${sessionsList.find(s => s.session_id === sessionToDeleteId)?.candidate_name} (Job: ${sessionsList.find(s => s.session_id === sessionToDeleteId)?.job_title})`}
              .
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setShowDeleteConfirm(false)}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteSession}
              className="bg-red-600 hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
    </PageTransition>
  );
}
