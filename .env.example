# Copy this file to `.env` (root) for local development.
# Values below match docker-compose defaults.

# === Backend ===
DATABASE_URL=postgresql+asyncpg://user:password@postgres:5432/interview_platform
SECRET_KEY=supersecret-for-dev
JUDGE0_URL=http://judge0:2358
REDIS_URL=redis://redis:6379/0

# === Frontend ===
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000

# === Admin Defaults (created automatically on first backend start) ===
ADMIN_USERNAME=admin
ADMIN_PASSWORD=admin

# -- AI Analysis Service --

# LLM Provider: "google" or "openrouter"
LLM_PROVIDER=openrouter

# For Google (LLM_PROVIDER=google)
GOOGLE_API_KEY=your_google_api_key_here

# For OpenRouter (LLM_PROVIDER=openrouter)
OPENROUTER_API_KEY=your_openrouter_api_key_here
# OPENROUTER_API_BASE=https://openrouter.ai/api/v1 # Optional: Override default base URL
MODEL_MAX_RETRIES=2

# Optional: Specify custom model names. 
# For Google, use the model name directly (e.g., "gemini-2.5-pro").
# For OpenRouter, use the provider/model format (e.g., "google/gemini-2.5-pro").
SOLUTION_MODEL=google/gemini-2.5-flash-lite-preview-06-17
ANALYSIS_MODEL=google/gemini-2.5-flash
EXPERT_MODEL=google/gemini-2.5-pro

# -- MLflow & LangSmith Tracing --

# Optional: Set the MLflow tracking URI to enable experiment logging and tracing.
# The docker-compose setup runs MLflow on port 5001.
MLFLOW_TRACKING_URI=http://127.0.0.1:5001

# Optional: Set a project name for LangSmith tracing.
LANGCHAIN_PROJECT=code-guardians-analysis
