"""Centralized logging configuration for Code Guardians platform."""
import logging
import logging.config
import logging.handlers
import os
import sys
from pathlib import Path
from typing import Any, Dict

import structlog
from structlog.types import FilteringBoundLogger


def setup_file_logging() -> None:
    """
    Configure file-based logging with rotation for production environments.
    """
    # Create logs directory if it doesn't exist
    log_dir = Path(os.getenv("LOG_DIR", "/app/logs"))
    log_dir.mkdir(parents=True, exist_ok=True)

    # Configure rotating file handler
    log_file = log_dir / "code_guardians.log"
    max_bytes = int(os.getenv("LOG_MAX_BYTES", "10485760"))  # 10MB default
    backup_count = int(os.getenv("LOG_BACKUP_COUNT", "5"))

    file_handler = logging.handlers.RotatingFileHandler(
        filename=str(log_file),
        maxBytes=max_bytes,
        backupCount=backup_count,
        encoding='utf-8'
    )

    # Set formatter for file output
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    file_handler.setFormatter(file_formatter)

    # Add file handler to root logger
    root_logger = logging.getLogger()
    root_logger.addHandler(file_handler)


def configure_logging() -> FilteringBoundLogger:
    """
    Configure structured logging for the application.
    
    Returns:
        FilteringBoundLogger: Configured structlog logger instance
    """
    # Get environment settings
    log_level = os.getenv("LOG_LEVEL", "INFO").upper()
    environment = os.getenv("ENVIRONMENT", "development").lower()
    enable_file_logging = os.getenv("ENABLE_FILE_LOGGING", "false").lower() == "true"

    # Configure standard library logging
    logging.basicConfig(
        format="%(message)s",
        stream=sys.stdout,
        level=getattr(logging, log_level, logging.INFO),
    )

    # Setup file logging if enabled (typically for production)
    if enable_file_logging or environment == "production":
        setup_file_logging()
    
    # Configure structlog processors based on environment
    processors = [
        structlog.contextvars.merge_contextvars,
        structlog.processors.add_log_level,
        structlog.processors.StackInfoRenderer(),
        structlog.dev.set_exc_info,
    ]
    
    if environment == "production":
        # Production: JSON formatting for log aggregation
        processors.extend([
            structlog.processors.TimeStamper(fmt="iso"),
            structlog.processors.JSONRenderer()
        ])
    else:
        # Development: Human-readable formatting
        try:
            import colorama
            colorama.init()
            console_renderer = structlog.dev.ConsoleRenderer(colors=True)
        except (ImportError, SystemError):
            # Fallback to no colors if colorama is not available
            console_renderer = structlog.dev.ConsoleRenderer(colors=False)

        processors.extend([
            structlog.processors.TimeStamper(fmt="%Y-%m-%d %H:%M:%S"),
            console_renderer
        ])
    
    # Configure structlog
    structlog.configure(
        processors=processors,
        wrapper_class=structlog.make_filtering_bound_logger(
            getattr(logging, log_level, logging.INFO)
        ),
        logger_factory=structlog.PrintLoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    return structlog.get_logger("code_guardians")


def get_logger(name: str = "code_guardians") -> FilteringBoundLogger:
    """
    Get a logger instance with the given name.
    
    Args:
        name: Logger name (typically module name)
        
    Returns:
        FilteringBoundLogger: Logger instance
    """
    return structlog.get_logger(name)


def log_context(**kwargs: Any) -> Dict[str, Any]:
    """
    Create a logging context dictionary.
    
    Args:
        **kwargs: Context key-value pairs
        
    Returns:
        Dict[str, Any]: Context dictionary for logging
    """
    return kwargs


# Common logging contexts for the application
class LogContext:
    """Common logging context helpers."""
    
    @staticmethod
    def session(session_id: str, user_type: str = None) -> Dict[str, Any]:
        """Create session context."""
        context = {"session_id": session_id}
        if user_type:
            context["user_type"] = user_type
        return context
    
    @staticmethod
    def request(method: str, path: str, request_id: str = None) -> Dict[str, Any]:
        """Create HTTP request context."""
        context = {"http_method": method, "http_path": path}
        if request_id:
            context["request_id"] = request_id
        return context
    
    @staticmethod
    def database(operation: str, table: str = None) -> Dict[str, Any]:
        """Create database operation context."""
        context = {"db_operation": operation}
        if table:
            context["db_table"] = table
        return context
    
    @staticmethod
    def ai_analysis(session_id: str, stage: str = None) -> Dict[str, Any]:
        """Create AI analysis context."""
        context = {"session_id": session_id, "component": "ai_analysis"}
        if stage:
            context["analysis_stage"] = stage
        return context
    
    @staticmethod
    def code_execution(session_id: str, language: str = None) -> Dict[str, Any]:
        """Create code execution context."""
        context = {"session_id": session_id, "component": "code_execution"}
        if language:
            context["language"] = language
        return context
    
    @staticmethod
    def websocket(event: str, session_id: str = None) -> Dict[str, Any]:
        """Create WebSocket event context."""
        context = {"component": "websocket", "ws_event": event}
        if session_id:
            context["session_id"] = session_id
        return context


# Performance logging helpers
class PerformanceLogger:
    """Helper for logging performance metrics."""
    
    def __init__(self, logger: FilteringBoundLogger, operation: str, **context):
        self.logger = logger
        self.operation = operation
        self.context = context
        self.start_time = None
    
    def __enter__(self):
        import time
        self.start_time = time.time()
        self.logger.info(
            f"Starting {self.operation}",
            operation=self.operation,
            **self.context
        )
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        import time
        duration = time.time() - self.start_time

        if exc_type is None:
            self.logger.info(
                f"Completed {self.operation}",
                operation=self.operation,
                duration_seconds=round(duration, 3),
                **self.context
            )
        else:
            self.logger.error(
                f"Failed {self.operation}",
                operation=self.operation,
                duration_seconds=round(duration, 3),
                error=str(exc_val),
                **self.context
            )

    def start(self):
        """Start manual timing."""
        import time
        self.start_time = time.time()
        self.logger.info(
            f"Starting {self.operation}",
            operation=self.operation,
            **self.context
        )

    def end(self):
        """End manual timing."""
        import time
        if self.start_time is None:
            self.logger.warning(f"PerformanceLogger.end() called without start() for {self.operation}")
            return

        duration = time.time() - self.start_time
        self.logger.info(
            f"Completed {self.operation}",
            operation=self.operation,
            duration_seconds=round(duration, 3),
            **self.context
        )


# Initialize the main logger
logger = configure_logging()
