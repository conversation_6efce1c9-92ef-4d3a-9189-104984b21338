"""Interview session endpoints."""
import os
import secrets
from uuid import UUID # Added for session_id conversion
from datetime import datetime, timed<PERSON><PERSON>
from typing import Annotated

from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.security import OAuth2<PERSON>asswordBearer
from pydantic import BaseModel, Field, UUID4, ConfigDict
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from app.database import get_db
from ..core.security import decode_access_token
from ..models.admin import Admin
from ..models.schemas import InterviewSession, CodeChange, InterviewArtifact
from ..sockets import sio

router = APIRouter(prefix="/sessions", tags=["sessions"])

oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/auth/login")


class SessionCreate(BaseModel):
    candidate_name: str
    interviewer_name: str
    job_title: str
    language: str | None = None
    duration_minutes: int = 120


class SessionCreateResponse(BaseModel):
    session_id: str
    candidate_url: str
    interviewer_url: str


class SessionResponse(BaseModel):
    session_id: str
    candidate_url: str
    interviewer_url: str
    expires_at: datetime
    candidate_name: str
    interviewer_name: str
    job_title: str
    created_at: datetime
    is_active: bool
    language: str | None = None


class SessionStatusUpdate(BaseModel):
    is_active: bool


class SessionDetailsResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    candidate_name: str
    interviewer_name: str
    job_title: str
    created_at: datetime
    expires_at: datetime


class LatestCodeResponse(BaseModel):
    session_id: str
    latest_code_snapshot: str | None = None


class LanguageResponse(BaseModel):
    language: str | None = None


class InterviewArtifactCreate(BaseModel):
    artifact_type: str  # 'question' or 'expected_output'
    content: str


class InterviewArtifactResponse(BaseModel):
    model_config = ConfigDict(from_attributes=True)

    id: int
    session_id: UUID4
    artifact_type: str
    content: str
    created_at: datetime


class LatestArtifactsResponse(BaseModel):
    latest_question: InterviewArtifactResponse | None = None
    latest_output: InterviewArtifactResponse | None = None


async def get_current_admin(token: Annotated[str, Depends(oauth2_scheme)], db: AsyncSession = Depends(get_db)) -> Admin:
    try:
        payload = decode_access_token(token)
        admin_id: int | None = int(payload.get("sub")) if payload else None
    except Exception:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    result = await db.execute(select(Admin).where(Admin.id == admin_id))
    admin: Admin | None = result.scalar_one_or_none()
    if admin is None:
        raise HTTPException(status_code=status.HTTP_401_UNAUTHORIZED, detail="Invalid credentials")
    return admin


async def get_session_participant(
    session_id: UUID4,
    db: AsyncSession = Depends(get_db),
    session_token: str | None = Query(None, alias="token"),
) -> str:
    if not session_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Session token required",
            headers={"WWW-Authenticate": "Bearer"}, # Added for clarity, though not strictly Bearer here
        )

    session = await db.get(InterviewSession, session_id)
    if not session:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Session not found")

    if session.candidate_token == session_token:
        return "candidate"
    elif session.interviewer_token == session_token:
        return "interviewer"
    else:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invalid or expired session token",
        )


@router.get("/", response_model=list[SessionResponse])
async def list_sessions(db: AsyncSession = Depends(get_db)):
    result = await db.execute(select(InterviewSession).order_by(InterviewSession.created_at.desc()))
    sessions = result.scalars().all()
    frontend_domain = os.getenv("FRONTEND_BASE", "http://localhost:3000")
    responses: list[SessionResponse] = []
    for session in sessions:
        candidate_url = f"{frontend_domain}/interview/candidate/{session.id}?token={session.candidate_token}"
        interviewer_url = f"{frontend_domain}/interview/interviewer/{session.id}?token={session.interviewer_token}"
        responses.append(
            SessionResponse(
                session_id=str(session.id),
                candidate_url=candidate_url,
                interviewer_url=interviewer_url,
                expires_at=session.expires_at,
                candidate_name=session.candidate_name,
                interviewer_name=session.interviewer_name,
                job_title=session.job_title,
                created_at=session.created_at,
                is_active=session.is_active,
                language=session.language,
            )
        )
    return responses


@router.post("/", response_model=SessionResponse)
async def create_session(
    payload: SessionCreate,
    db: AsyncSession = Depends(get_db),
    _: Admin = Depends(get_current_admin),
):
    expires_at = datetime.utcnow() + timedelta(minutes=payload.duration_minutes)
    candidate_token = secrets.token_urlsafe(16)
    interviewer_token = secrets.token_urlsafe(16)

    session = InterviewSession(
        admin_id=1,  # TODO: replace with actual admin.id once auth wiring is complete
        candidate_name=payload.candidate_name,
        interviewer_name=payload.interviewer_name,
        job_title=payload.job_title,
        language=payload.language,
        candidate_token=candidate_token,
        interviewer_token=interviewer_token,
        expires_at=expires_at,
    )
    db.add(session)
    await db.commit()
    await db.refresh(session)

    frontend_domain = os.getenv("FRONTEND_BASE", "http://localhost:3000")
    candidate_url = f"{frontend_domain}/interview/candidate/{session.id}?token={candidate_token}"
    interviewer_url = f"{frontend_domain}/interview/interviewer/{session.id}?token={interviewer_token}"

    return SessionResponse(
        session_id=str(session.id),
        candidate_url=candidate_url,
        interviewer_url=interviewer_url,
        expires_at=expires_at,
        candidate_name=session.candidate_name,
        interviewer_name=session.interviewer_name,
        job_title=session.job_title,
        created_at=session.created_at, # Ensure all fields from SessionResponse are present
        is_active=session.is_active,
        language=session.language,
    )


@router.patch("/{session_id}/status", response_model=SessionResponse)
async def update_session_status(
    session_id: str,
    payload: SessionStatusUpdate,
    db: AsyncSession = Depends(get_db),
    _: Admin = Depends(get_current_admin),
):
    try:
        session_uuid = UUID(session_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format. Must be a valid UUID."
        )

    result = await db.execute(
        select(InterviewSession).where(InterviewSession.id == session_uuid)
    )
    session: InterviewSession | None = result.scalar_one_or_none()

    if session is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
        )

    session.is_active = payload.is_active
    await db.commit()
    await db.refresh(session)

    frontend_domain = os.getenv("FRONTEND_BASE", "http://localhost:3000")
    candidate_url = f"{frontend_domain}/interview/candidate/{session.id}?token={session.candidate_token}"
    interviewer_url = f"{frontend_domain}/interview/interviewer/{session.id}?token={session.interviewer_token}"

    return SessionResponse(
        session_id=str(session.id),
        candidate_url=candidate_url,
        interviewer_url=interviewer_url,
        expires_at=session.expires_at,
        candidate_name=session.candidate_name,
        interviewer_name=session.interviewer_name,
        job_title=session.job_title,
        created_at=session.created_at,
        is_active=session.is_active,
        language=session.language,
    )


@router.delete("/{session_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_session(
    session_id: str,
    db: AsyncSession = Depends(get_db),
    _: Admin = Depends(get_current_admin),
):
    try:
        session_uuid = UUID(session_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format. Must be a valid UUID."
        )

    result = await db.execute(
        select(InterviewSession).where(InterviewSession.id == session_uuid)
    )
    session: InterviewSession | None = result.scalar_one_or_none()

    if session is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
        )

    await db.delete(session)
    await db.commit()
    return None # FastAPI will return 204 No Content


@router.get("/{session_id}/latest_code", response_model=LatestCodeResponse)
async def get_latest_code(
    session_id: str,
    db: AsyncSession = Depends(get_db),
    # Add current_user dependency if you want to restrict access, e.g., only to participants or admin
    # _: Admin = Depends(get_current_admin), # Or a new dependency for candidate/interviewer
):
    try:
        session_uuid = UUID(session_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format. Must be a valid UUID."
        )

    # First, check if the session itself exists to provide a more specific error
    session_check_result = await db.execute(
        select(InterviewSession).where(InterviewSession.id == session_uuid)
    )
    if session_check_result.scalar_one_or_none() is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
        )

    # Fetch the latest code change event for this session
    latest_code_change_result = await db.execute(
        select(CodeChange.full_code_snapshot)
        .where(CodeChange.session_id == session_uuid)
        .order_by(CodeChange.created_at.desc())
        .limit(1)
    )
    latest_snapshot: str | None = latest_code_change_result.scalar_one_or_none()

    return LatestCodeResponse(
        session_id=session_id,
        latest_code_snapshot=latest_snapshot if latest_snapshot is not None else ""
    )


@router.get("/{session_id}/language", response_model=LanguageResponse)
async def get_session_language(session_id: str, db: AsyncSession = Depends(get_db)):
    try:
        session_uuid = UUID(session_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format. Must be a valid UUID."
        )

    result = await db.execute(
        select(InterviewSession.language).where(InterviewSession.id == session_uuid)
    )
    language = result.scalar_one_or_none()

    if language is None:
        # This also implicitly handles the case where the session doesn't exist,
        # though the error message could be more specific if we did a separate session check first.
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Session or language not found"
        )

    return LanguageResponse(language=language)


@router.post("/{session_id}/artifacts", response_model=InterviewArtifactResponse)
async def create_interview_artifact(
    session_id: str,
    payload: InterviewArtifactCreate,
    db: AsyncSession = Depends(get_db),
    # Add auth dependency later if needed
):
    try:
        session_uuid = UUID(session_id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid session ID format. Must be a valid UUID."
        )

    # Verify session exists
    session_check_result = await db.execute(
        select(InterviewSession).where(InterviewSession.id == session_uuid)
    )
    if session_check_result.scalar_one_or_none() is None:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Session not found"
        )

    new_artifact = InterviewArtifact(
        session_id=session_uuid,
        artifact_type=payload.artifact_type,
        content=payload.content,
    )
    db.add(new_artifact)
    await db.commit()
    await db.refresh(new_artifact)

    response_data = InterviewArtifactResponse(
        id=new_artifact.id,
        session_id=str(new_artifact.session_id),
        artifact_type=new_artifact.artifact_type,
        content=new_artifact.content,
        created_at=new_artifact.created_at,
    )

    # Broadcast to WebSocket room
    room = f"session_{session_id}"
    await sio.emit("artifact_presented", response_data.model_dump(mode='json'), room=room)

    return response_data


@router.get("/{session_id}/details", response_model=SessionDetailsResponse)
async def get_session_details(
    session_id: UUID4,
    db: AsyncSession = Depends(get_db),
    # Use the new dependency to ensure the requester is a participant
    _participant_role: str = Depends(get_session_participant),
): # noqa: D103
    session = await db.get(InterviewSession, session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    return SessionDetailsResponse(
        candidate_name=session.candidate_name,
        interviewer_name=session.interviewer_name,
        job_title=session.job_title,
        created_at=session.created_at,
        expires_at=session.expires_at,
    )


@router.get("/{session_id}/latest_artifacts", response_model=LatestArtifactsResponse)
async def get_latest_artifacts(
    session_id: UUID4,
    db: AsyncSession = Depends(get_db),
    _participant_role: str = Depends(get_session_participant),
): # noqa: D103
    latest_question_stmt = (
        select(InterviewArtifact)
        .where(
            InterviewArtifact.session_id == session_id,
            InterviewArtifact.artifact_type == "question",
        )
        .order_by(InterviewArtifact.created_at.desc())
        .limit(1)
    )
    latest_question_result = await db.execute(latest_question_stmt)
    latest_question_db = latest_question_result.scalar_one_or_none()

    latest_output_stmt = (
        select(InterviewArtifact)
        .where(
            InterviewArtifact.session_id == session_id,
            InterviewArtifact.artifact_type == "expected_output",
        )
        .order_by(InterviewArtifact.created_at.desc())
        .limit(1)
    )
    latest_output_result = await db.execute(latest_output_stmt)
    latest_output_db = latest_output_result.scalar_one_or_none()

    return LatestArtifactsResponse(
        latest_question=InterviewArtifactResponse.model_validate(latest_question_db) if latest_question_db else None,
        latest_output=InterviewArtifactResponse.model_validate(latest_output_db) if latest_output_db else None,
    )
