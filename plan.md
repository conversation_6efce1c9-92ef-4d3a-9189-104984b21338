# Real-time Collaborative Coding Interview Platform - POC

## Notes
- Platform is a proof-of-concept for AI-powered cheating detection in coding interviews.
- Data collection (keystrokes, code changes, execution events) is the highest priority.
- Tech stack: Next.js (React 19, TypeScript), Tailwind, Shadcn/ui, Monaco Editor, FastAPI, PostgreSQL, SQLAlchemy, Redis, Judge0, Docker Compose.
- Real-time code sync and execution for both candidate and interviewer via Socket.io.
- Security: Use secure tokens for session URLs; OAuth not required now.
- UI polish is appreciated but secondary to robust data logging.
- Architecture must be modular for future AI/ML integration.

## Task List
- [x] Set up project structure (frontend, backend, init-db.sql) 
- [x] Create docker-compose.yml for all services 
- [x] Backend: Implement FastAPI app skeleton 
- [x] Backend: Design and implement SQLAlchemy models for provided schema 
- [x] Backend: Implement admin login and session creation endpoints 
- [x] Backend: Implement WebSocket endpoints for real-time sync (Socket.io) 
- [x] Backend: Integrate go-judge for Python & Java code execution 
- [x] Backend: Implement event logging for keystrokes, code changes (full snapshots via sockets), execution events 
- [x] Backend: Replace Judge0 integration with go-judge (runner & REST API) 
- [x] docker-compose: Add `go-judge` service and remove `judge0` 
- [x] Backend: Implement GoJudgeService for Python & Java 
- [x] Frontend: Update UI to support language selection (Python/Java) 
- [x] Backend: Implement GET /sessions/{session_id}/latest_code endpoint to retrieve the most recent code snapshot 
- [ ] Backend: Optimize DB for high-frequency writes
- [x] Frontend: Set up Next.js app with Tailwind and Shadcn/ui 
- [x] Frontend: Implement admin dashboard (login, create sessions, generate URLs) 
- [x] Frontend: Implement candidate/interviewer interview pages with Monaco Editor 
- [x] Frontend: Implement real-time code sync via WebSocket 
- [x] Frontend: Capture and send keystroke/code change events (ensure fullCodeSnapshot is sent) from Monaco Editor 
- [x] Frontend: On interview page load, fetch and display latest code snapshot from GET /sessions/{session_id}/latest_code 
- [x] Frontend: Display execution output/errors in real-time 
- [x] Frontend: Professional UI/UX polish 
- [ ] Test multi-user, multi-session scenarios
- [ ] Verify comprehensive data logging in DB
- [x] Document setup and usage in README 

## Completed Milestones

### AI Cheating Detection (Phase 1) - COMPLETE
This phase built the foundational multi-agent AI analysis system using LangGraph and integrated it into the interviewer's UI.
- **[x] Backend: Setup LangGraph & Dependencies**
- **[x] Backend: Define LangGraph State & Schema**
- **[x] Backend: Implement LangGraph Agent Nodes** (Solution Generation, Initial Analysis, Expert Analysis)
- **[x] Backend: Build the LangGraph Workflow** with conditional routing.
- **[x] Backend: Integrate with Execution Flow** to trigger analysis asynchronously on code execution.
- **[x] Frontend: Update Interviewer UI** with collapsible `Accordion` for analysis display.
- **[x] Frontend: Trigger and Display AI Analysis** with loading states and formatted results.

### MLflow Integration & Asynchronous Refactoring - COMPLETE
This phase focused on robust experiment tracking and resolving critical performance issues.
- **[x] Integrate MLflow** for experiment tracking and deep LangGraph tracing.
- **[x] Configure MLflow server in `docker-compose.yml`** with a bind mount for persistent artifact storage, resolving 404 errors on trace views.
- **[x] Refactor AI analysis to run asynchronously** in a background thread, preventing backend freezes and ensuring a responsive UI.
- **[x] Fix WebSocket broadcasting** of analysis results to ensure the frontend is updated reliably.

### UI/UX Polish (June 2025) - COMPLETE
- **[x] Update navbar logo** and fix root route redirect.
- **[x] Define color palette and font stack** and integrate into Tailwind config.
- **[x] Create reusable UI components** (`Button`, `Card`, `Badge`).
- **[x] Polish Admin Dashboard** layout and session creation flow.
- **[x] Implement Code Persistence** on refresh for the interview page.
- **[x] Render AI analysis explanations as formatted markdown** in the frontend for improved readability.

## Current Goal
Start integrating AI for cheating detection. On the interviewer's screen, we need to display an 'AI analysis' section. This section should display the AI's analysis of the candidate's solution. This analysis should be based on the candidate's code and the question from the interviewer. We should be able to use more than one AI agent to perform this analysis. There should be an AI agent, using a smaller model, like gemini-2.5-flash-lite-preview-06-17, for generating a few solutions for the question. Then, we should use a another agent with a larger model, like gemini-2.5-flash, to analyze the candidate's solution and compare it with the generated solutions. If the candidate's solution is similar to the generated solutions, then the AI should flag it as a potential cheating attempt and it should be displayed in the 'AI analysis' section with a brief explanation and a probability. If the agent with the larger model is unable to determine either way, i.e., whether the candidate is cheating or not, then it should invoke another agent with an even larger model, like gemini-2.5-pro, to analyze the candidate's solution and compare it with the generated solutions. The trigger for the 'AI analysis' should be the click of the 'Run' button by either the candidate or the interviewer. To make space for the 'AI Analysis' section, we should make the 'Question to present' and 'Expected output' sections collapsible in the Interview Tools area for the interviewer. The 'AI analysis' section should be collapsible as well. This solution for AI cheating detection is just a starting point and the solution is not finalized. It can be tweaked and even overhauled during implementation if a better approach is identified.

## UI/UX Polishing Roadmap (June 2025)

| Priority | Task | Owner | Status |
|----------|------|-------|--------|
| P0 | Update navbar logo to `<> Code Guardians` | Cascade | ✅ Done (19 Jun 2025)
| P0 | Root route `/` should redirect to admin dashboard (or login) instead of 404 | Cascade | ✅ Done (19 Jun 2025)
| P1 | Define colour palette (primary, secondary, accent, neutral   shades) & add to Tailwind config | Cascade | ✅ Done (19 Jun 2025)
| P1 | Choose font stack (e.g. `Inter` for UI + `JetBrains Mono` for code) and import via `@next/font/google` | Cascade | ✅ Done (19 Jun 2025)
| P1 | Create reusable `Button`, `Card`, `Badge` components using new palette | Cascade | ✅ Done (19 Jun 2025)
| P1 | Polish Admin Dashboard layout (stats cards, session list as per mock-up) | Cascade | ✅ Done (19 Jun 2025)
| P1 | Admin Dashboard: Enhance session creation with auto-refresh and persistent, dismissible new session links panel | Cascade | ✅ Done (20 Jun 2025)
| P1 | Enrich session list with candidate & interviewer names, job title, creation date, and status | Cascade/Frontend | ✅ Done (20 Jun 2025)
| P2 | Add micro-animations with `framer-motion` (page transitions, card hover, button tap) | Cascade | ✅ Done (20 Jun 2025)
| P1 | Apply Shadcn UI "New York" style with blue theme variant | Cascade | ✅ Done (20 Jun 2025)
| P2 | Create dark theme variant matching palette | Cascade | ⏳

> **Note:** Frontend polish may require corresponding backend & DB changes (e.g. storing candidate names). Each change will be implemented incrementally with coordinated migrations.

## Backend API Roadmap (June 2025)

| Priority | Endpoint | Purpose | Owner | Status |
|----------|----------|---------|-------|--------|
| P0 | `GET /stats` | Counts for dashboard cards | Backend | Done |
| P0 | `POST /sessions` | Create session (accepts candidate_name, interviewer_name, job_title) | Backend | Done |
| P0 | `GET /sessions` | Paginated list with candidate & interviewer names, job title, created_at, is_active | Backend | ✅ Done |
| P0 | `keystroke` (WebSocket event) | Persist keystroke events | Backend | ✅ Done (via Sockets) |
| P0 | `code_change` (WebSocket event) | Persist code changes including full snapshots | Backend | ✅ Done (via Sockets) |
| P0 | `execution_result` (WebSocket event) | Persist compile/execute events & results | Backend | ✅ Done (via Sockets) |
| P0 | `GET /sessions/{id}/latest_code` | Retrieve latest code snapshot for a session | Backend | ✅ Done (20 Jun 2025) |
| P1 | `PATCH /sessions/{id}` | Mark session finished / archived | Backend | ⏳ Todo |
| P2 | `POST /auth/refresh` | JWT refresh flow | Backend | ⏳ Future |
| P2 | `CRUD /admin/users` | Manage admin accounts | Backend | ⏳ Future |